import type { ModelInfo } from "../model.js"

// ## AI 程序员模型列表

// | 模型              | 输入        | 输出   | 推理框架 |
// |-------------------|-------------|--------|----------|
// | DeepSeekV3        | 48k         | 16k    | mindie   |
// | DeepSeekR1        | 48k         | 16k    | mindie   |
// | DeepSeekR1-0528   | 16k         | 16k    | mindie   |
// | jiutian-75b       | 4k          | 4k     | mindie   |
// | zhanlu            | 112k        | 16k    | mindie   |
// | zhanlu-r1         | 24k（32k）  | 8k     | vllm     |

// AI程序员：model字段：zhanluAI、deepseek-v3，对应我传给智算平台的模型:zhanlu、default；
// 智能问答：model字段：zhanlu、zhanlu-r1、deepseek-v3、deepseek-r1、jiutian-75b，对应我传给模型：zhanlu、zhanlu-r1、deepseek-v3、deepseek-r1、jiutian-75b
// Zhanlu model definitions
export type ZhanluModelId = keyof typeof zhanluModels
export const zhanluDefaultModelId: ZhanluModelId = "zhanluAI"
export const zhanluModels = {
	zhanluAI: {
		maxTokens: 16000,
		contextWindow: 128000,
		supportsImages: false,
		supportsPromptCache: false,
		inputPrice: 0, // 设为0，使其免费
		outputPrice: 0, // 设为0，使其免费
		description: "湛卢代码大模型",
	},
	// 暂时屏蔽非AI程序的模型，因为这些模型都是在服务端做了Promote封装和设计，不是单纯的openai的api调用
	// zhanlu: {
	// 	maxTokens: 4096,
	// 	contextWindow: 32768,
	// 	supportsImages: false,
	// 	supportsPromptCache: false,
	// 	inputPrice: 0.002, // per 1k tokens
	// 	outputPrice: 0.002, // per 1k tokens
	// 	description: "湛卢大模型",
	// },
	"zhanlu-r1": {
		maxTokens: 8000,
		contextWindow: 32000,
		supportsImages: false,
		supportsPromptCache: false,
		inputPrice: 0, // free
		outputPrice: 0, // free
		description: "湛卢r1模型",
	},
	"deepseek-v3": {
		maxTokens: 16000,
		contextWindow: 64000,
		supportsImages: false,
		supportsPromptCache: false,
		inputPrice: 0, // 设为0，使其免费
		outputPrice: 0, // 设为0，使其免费
		description: "DeepSeek V3大模型",
	},
	"deepseek-r1": {
		maxTokens: 16000,
		contextWindow: 64000,
		supportsImages: false,
		supportsPromptCache: false,
		inputPrice: 0, // free
		outputPrice: 0, // free
		description: "DeepSeek r1模型",
	},
	"deepseek-r1-0528": {
		maxTokens: 16000,
		contextWindow: 32000,
		supportsImages: false,
		supportsPromptCache: false,
		inputPrice: 0, // free
		outputPrice: 0, // free
		description: "DeepSeek r1-0528模型",
	},
	"jiutian-75b": {
		maxTokens: 4000,
		contextWindow: 8000,
		supportsImages: false,
		supportsPromptCache: false,
		inputPrice: 0, // free
		outputPrice: 0, // free
		description: "九天75b大模型",
	},
} as const satisfies Record<string, ModelInfo>
