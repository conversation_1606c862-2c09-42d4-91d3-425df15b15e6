import { z } from "zod"

import type { Keys, Equals, AssertEqual } from "./type-fu.js"

/**
 * ExperimentId
 */

export const experimentIds = [
	"powerSteering",
	"disableCompletionCommand",
	"marketplace",
	"multiFileApplyDiff",
	"roomoteAgent",
] as const

export const experimentIdsSchema = z.enum(experimentIds)

export type ExperimentId = z.infer<typeof experimentIdsSchema>

/**
 * Experiments
 */

export const experimentsSchema = z.object({
	powerSteering: z.boolean().optional(),
	disableCompletionCommand: z.boolean().optional(),
	marketplace: z.boolean().optional(),
	multiFileApplyDiff: z.boolean().optional(),
	roomoteAgent: z.boolean().optional(),
})

export type Experiments = z.infer<typeof experimentsSchema>

type _AssertExperiments = AssertEqual<Equals<ExperimentId, Keys<Experiments>>>
