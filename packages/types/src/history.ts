import { z } from "zod"

/**
 * HistoryItem
 */

export const historyItemSchema = z.object({
	id: z.string(),
	number: z.number(),
	ts: z.number(),
	task: z.string(),
	tokensIn: z.number(),
	tokensOut: z.number(),
	cacheWrites: z.number().optional(),
	cacheReads: z.number().optional(),
	totalCost: z.number(),
	size: z.number().optional(),
	workspace: z.string().optional(),
	internetSearchResults: z
		.array(
			z.object({
				requestId: z.string(),
				url: z.string(),
				title: z.string(),
			}),
		)
		.optional(),
	searchInternetsRequestId: z.string().optional(),
	internetSearchError: z.string().optional(),
	// Remote Agent 支持
	taskType: z.enum(["local", "remote"]).default("local"),
	remoteTaskId: z.string().optional(),
	remoteJobId: z.string().optional(),
	roomoteApiUrl: z.string().optional(),
	remoteStatus: z.enum(["pending", "processing", "completed", "failed"]).optional(),
	remoteConfig: z
		.object({
			mode: z.string().optional(),
			profileName: z.string().optional(),
			jiraIssue: z.string().optional(),
			gitlabProject: z.string().optional(),
			gitlabBranch: z.string().optional(),
		})
		.optional(),
})

export type HistoryItem = z.infer<typeof historyItemSchema>
