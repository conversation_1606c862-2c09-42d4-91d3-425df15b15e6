{"name": "@roo-code/cloud", "description": "Roo Code Cloud VSCode integration.", "version": "0.0.0", "type": "module", "exports": "./src/index.ts", "scripts": {"lint": "eslint src --ext=ts --max-warnings=0", "check-types": "tsc --noEmit", "test": "vitest run", "clean": "rimraf dist .turbo"}, "dependencies": {"@roo-code/telemetry": "workspace:^", "@roo-code/types": "workspace:^", "axios": "^1.7.4", "zod": "^3.25.61"}, "devDependencies": {"@roo-code/config-eslint": "workspace:^", "@roo-code/config-typescript": "workspace:^", "@types/node": "20.x", "@types/vscode": "^1.84.0", "vitest": "^3.2.3"}}