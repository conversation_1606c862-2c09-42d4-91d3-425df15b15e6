{"extension.displayName": "<PERSON><PERSON><PERSON>", "extension.description": "Un intero team di sviluppo di agenti IA nel tuo editor.", "command.newTask.title": "Nuovo Task", "command.explainCode.title": "Spiega Codice", "command.fixCode.title": "<PERSON><PERSON><PERSON><PERSON>", "command.improveCode.title": "<PERSON><PERSON><PERSON>", "command.unitTest.title": "Test Unitario", "command.codeReview.title": "Revisione del codice", "command.commentCode.title": "Commenta Codice", "command.addToContext.title": "Aggiungi al Contesto", "command.openInNewTab.title": "Apri in Nuova Scheda", "command.focusInput.title": "Focalizza Campo di Input", "command.setCustomStoragePath.title": "Imposta Percorso di Archiviazione Personalizzato", "command.terminal.addToContext.title": "Aggiungi Contenuto del Terminale al Contesto", "command.terminal.fixCommand.title": "Corregg<PERSON>", "command.terminal.explainCommand.title": "Spiega Questo Comando", "command.acceptInput.title": "Accetta Input/Suggerimento", "views.activitybar.title": "<PERSON><PERSON><PERSON>", "views.contextMenu.label": "<PERSON><PERSON><PERSON>", "views.terminalMenu.label": "<PERSON><PERSON><PERSON>", "views.sidebar.name": "<PERSON><PERSON><PERSON>", "command.mcpServers.title": "Server MCP", "command.prompts.title": "<PERSON><PERSON>", "command.history.title": "Cronologia", "command.marketplace.title": "Marketplace", "command.openInEditor.title": "<PERSON><PERSON> nell'Editor", "command.settings.title": "Impostazioni", "command.documentation.title": "Documentazione", "configuration.title": "<PERSON><PERSON><PERSON>", "commands.allowedCommands.description": "Comandi che possono essere eseguiti automaticamente quando 'Approva sempre le operazioni di esecuzione' è attivato", "settings.vsCodeLmModelSelector.description": "Impostazioni per l'API del modello linguistico VSCode", "settings.vsCodeLmModelSelector.vendor.description": "Il fornitore del modello linguistico (es. copilot)", "settings.vsCodeLmModelSelector.family.description": "La famiglia del modello linguistico (es. gpt-4)", "settings.customStoragePath.description": "Percorso di archiviazione personalizzato. Lasciare vuoto per utilizzare la posizione predefinita. Supporta percorsi assoluti (es. 'D:\\ZhanluStorage')", "settings.completion.debounce_time.description": "Ritardo di attivazione del completamento del codice in millisecondi (ms)", "settings.completion.completion_number.description": "Numero di candidati generati dal completamento del codice", "settings.completion.inlineCompletion_granularity.description": "Preferenza per la granularità del completamento", "settings.completion.inlineCompletion_granularity.singleRow": "Singola riga", "settings.completion.inlineCompletion_granularity.oneTimeMaximization": "Massimizzazione una tantum", "settings.completion.inlineCompletion_granularity.balanced": "Bilanciato", "settings.completion.multiple_line_Completion.description": "Modalità di completamento su più righe", "settings.completion.multiple_line_Completion.autoCompletion": "Completamento automatico", "settings.completion.multiple_line_Completion.triggerCompletion": "Completamento attivato", "settings.completion.multiple_line_Completion.autoCompletion.description": "Invio e Ctrl+K (Cmd+K su Mac) possono attivare il completamento su più righe", "settings.completion.multiple_line_Completion.triggerCompletion.description": "Solo Ctrl+K (Cmd+K su Mac) attiva il completamento su più righe, Invio non lo attiva", "settings.completion.max_tokens_completion.description": "max_tokens per il completamento del codice", "settings.dmt.maxTokens.description": "max_tokens di codice grafico per domande e risposte multimodali", "settings.serverBaseUrl.description": "URL base per il server <PERSON>han<PERSON>, di default è https://api-wuxi-1.cmecloud.cn:8443"}