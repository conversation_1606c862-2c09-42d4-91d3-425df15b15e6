// 增强的跨文件感知功能测试
import * as vscode from "vscode"
import { vi, describe, it, expect, beforeEach } from "vitest"
import { ImportDefinitionsService } from "../ImportDefinitionsService"

describe("Enhanced ImportDefinitionsService", () => {
	let service: ImportDefinitionsService

	beforeEach(() => {
		service = new ImportDefinitionsService()
	})

	describe("isJavaBuiltinType", () => {
		it("should identify Java primitive types", () => {
			const service = new ImportDefinitionsService()
			const isBuiltinMethod = (service as any).isJavaBuiltinType.bind(service)

			expect(isBuiltinMethod("int")).toBe(true)
			expect(isBuiltinMethod("String")).toBe(true)
			expect(isBuiltinMethod("boolean")).toBe(true)
			expect(isBuiltinMethod("List")).toBe(true)
		})

		it("should not identify custom types as builtin", () => {
			const service = new ImportDefinitionsService()
			const isBuiltinMethod = (service as any).isJavaBuiltinType.bind(service)

			expect(isBuiltinMethod("CustomClass")).toBe(false)
			expect(isBuiltinMethod("MyService")).toBe(false)
			expect(isBuiltinMethod("DataModel")).toBe(false)
		})

		it("should identify short identifiers as builtin", () => {
			const service = new ImportDefinitionsService()
			const isBuiltinMethod = (service as any).isJavaBuiltinType.bind(service)

			expect(isBuiltinMethod("a")).toBe(true)
			expect(isBuiltinMethod("")).toBe(true)
			expect(isBuiltinMethod("1")).toBe(true)
			expect(isBuiltinMethod("123")).toBe(true)
		})
	})

	describe("isProjectRelatedPath", () => {
		it("should identify paths within workspace", () => {
			const service = new ImportDefinitionsService()
			const isProjectRelatedMethod = (service as any).isProjectRelatedPath.bind(service)

			const workspacePath = "/Users/<USER>/project"
			expect(isProjectRelatedMethod("/Users/<USER>/project/src/main/java/Test.java", workspacePath)).toBe(true)
			expect(isProjectRelatedMethod("/Users/<USER>/project/pom.xml", workspacePath)).toBe(true)
		})

		it("should identify related project modules", () => {
			const service = new ImportDefinitionsService()
			const isProjectRelatedMethod = (service as any).isProjectRelatedPath.bind(service)

			const workspacePath = "/Users/<USER>/project/module1"
			expect(isProjectRelatedMethod("/Users/<USER>/project/module2/src/main/java/Test.java", workspacePath)).toBe(
				true,
			)
			expect(isProjectRelatedMethod("/Users/<USER>/project/common/src/main/java/Utils.java", workspacePath)).toBe(
				true,
			)
		})

		it("should reject unrelated paths", () => {
			const service = new ImportDefinitionsService()
			const isProjectRelatedMethod = (service as any).isProjectRelatedPath.bind(service)

			const workspacePath = "/Users/<USER>/project"
			expect(isProjectRelatedMethod("/Users/<USER>/project/Test.java", workspacePath)).toBe(false)
			expect(isProjectRelatedMethod("/System/Library/Java/Test.java", workspacePath)).toBe(false)
		})
	})

	describe("fallbackJavaDefinitionSearch", () => {
		it("should handle missing workspace folder", async () => {
			const service = new ImportDefinitionsService()
			const fallbackMethod = (service as any).fallbackJavaDefinitionSearch.bind(service)

			const result = await fallbackMethod("TestClass", undefined)
			expect(result).toEqual([])
		})

		it("should extract class name from qualified import", async () => {
			const service = new ImportDefinitionsService()
			const fallbackMethod = (service as any).fallbackJavaDefinitionSearch.bind(service)

			const mockWorkspaceFolder = {
				uri: vscode.Uri.file("/test/workspace"),
				name: "test",
				index: 0,
			}

			// Test that it can extract class name from qualified import
			const result = await fallbackMethod("com.example.TestClass", mockWorkspaceFolder)

			// Should return empty array since no files are found in mock
			expect(result).toEqual([])
		})
	})

	describe("enhanced functionality", () => {
		it("should handle Java builtin types correctly", () => {
			const isBuiltinMethod = (service as any).isJavaBuiltinType.bind(service)

			expect(isBuiltinMethod("String")).toBe(true)
			expect(isBuiltinMethod("int")).toBe(true)
			expect(isBuiltinMethod("CustomClass")).toBe(false)
		})

		it("should validate project related paths", () => {
			const isProjectRelatedMethod = (service as any).isProjectRelatedPath.bind(service)
			const workspacePath = "/Users/<USER>/project"

			expect(isProjectRelatedMethod("/Users/<USER>/project/src/main/java/Test.java", workspacePath)).toBe(true)
			expect(isProjectRelatedMethod("/Users/<USER>/project/Test.java", workspacePath)).toBe(false)
		})
	})
})
