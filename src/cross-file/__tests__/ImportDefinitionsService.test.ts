// 简单的测试文件来验证跨文件感知功能
import * as vscode from "vscode"
import { ImportDefinitionsService } from "../ImportDefinitionsService"

describe("ImportDefinitionsService", () => {
	let service: ImportDefinitionsService

	beforeEach(() => {
		service = new ImportDefinitionsService()
	})

	describe("core functionality", () => {
		it("should create service instance", () => {
			expect(service).toBeDefined()
		})

		it("should have get method", () => {
			expect(typeof service.get).toBe("function")
		})
	})
})
