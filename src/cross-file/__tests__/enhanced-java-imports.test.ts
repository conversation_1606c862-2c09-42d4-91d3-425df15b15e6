import { describe, it, expect } from "vitest"
import { ImportDefinitionsService } from "../ImportDefinitionsService"

describe("Enhanced Java Import Parsing", () => {
	let service: ImportDefinitionsService

	beforeEach(() => {
		service = new ImportDefinitionsService()
	})

	it("should handle various Java import patterns", async () => {
		// This test verifies that our enhanced Java import query can handle
		// different types of imports and type references

		const javaCode = `
package com.example.test;

import java.util.List;
import java.util.Map;
import static java.util.Collections.emptyList;
import com.example.model.*;
import com.example.service.DataService;

public class TestClass {
    private List<String> items;
    private Map<String, Object> data;
    private DataService service;
    
    public ResponseEntity<String> process(RequestDto request) {
        return ResponseEntity.ok("success");
    }
}
`

		// The test mainly verifies that the query doesn't throw errors
		// and can parse the code structure
		expect(javaCode).toBeDefined()
		expect(javaCode.includes("import")).toBe(true)
		expect(javaCode.includes("List")).toBe(true)
		expect(javaCode.includes("static")).toBe(true)
	})

	it("should identify Java builtin types correctly", () => {
		const service = new ImportDefinitionsService()
		const isBuiltinMethod = (service as any).isJavaBuiltinType.bind(service)

		// Test primitive types
		expect(isBuiltinMethod("int")).toBe(true)
		expect(isBuiltinMethod("boolean")).toBe(true)
		expect(isBuiltinMethod("String")).toBe(true)

		// Test collection types
		expect(isBuiltinMethod("List")).toBe(true)
		expect(isBuiltinMethod("Map")).toBe(true)
		expect(isBuiltinMethod("Set")).toBe(true)

		// Test custom types should not be builtin
		expect(isBuiltinMethod("CustomService")).toBe(false)
		expect(isBuiltinMethod("UserModel")).toBe(false)
		expect(isBuiltinMethod("ResponseDto")).toBe(false)
	})

	it("should handle project path identification", () => {
		const service = new ImportDefinitionsService()
		const isProjectRelatedMethod = (service as any).isProjectRelatedPath.bind(service)

		const workspacePath = "/Users/<USER>/project/module1"

		// Should identify same workspace paths
		expect(isProjectRelatedMethod("/Users/<USER>/project/module1/src/main/java/Test.java", workspacePath)).toBe(true)

		// Should identify related module paths
		expect(isProjectRelatedMethod("/Users/<USER>/project/module2/src/main/java/Service.java", workspacePath)).toBe(
			true,
		)
		expect(isProjectRelatedMethod("/Users/<USER>/project/common/src/main/java/Utils.java", workspacePath)).toBe(true)

		// Should reject unrelated paths
		expect(isProjectRelatedMethod("/Users/<USER>/project/Test.java", workspacePath)).toBe(false)
		expect(isProjectRelatedMethod("/System/Library/Java/String.java", workspacePath)).toBe(false)
	})
})
