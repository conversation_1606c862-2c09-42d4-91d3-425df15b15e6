/* AIGC */
/* AIGC */
package com.chinamobile.cmss.acepilot.api.server.service.impl;

import com.chinamobile.cmss.acepilot.api.server.client.CodeClient;
import com.chinamobile.cmss.acepilot.api.server.client.model.CodeGenCompletionsParam;
import com.chinamobile.cmss.acepilot.api.server.client.model.CodeGenCompletionsRes;
import com.chinamobile.cmss.acepilot.api.server.client.model.OrderVerifiedRes;
import com.chinamobile.cmss.acepilot.api.server.client.util.ZhanluAESUtils;
import com.chinamobile.cmss.acepilot.api.server.enums.ServiceType;
import com.chinamobile.cmss.acepilot.server.common.utils.JSONUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.kafka.core.KafkaTemplate;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

public class CodeServiceImplTest {

    @InjectMocks
    private CodeServiceImpl codeServiceImpl;

    @Mock
    private CodeClient codeClient;

    @Mock
    private KafkaTemplate kafkaTemplate;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        codeServiceImpl = new CodeServiceImpl();
        
        // 使用反射设置私有字段
        setPrivateField(codeServiceImpl, "codeClient", codeClient);
        setPrivateField(codeServiceImpl, "kafkaTemplate", kafkaTemplate);
    }

    private void setPrivateField(Object targetObject, String fieldName, Object value) {
        try {
            java.lang.reflect.Field field = targetObject.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(targetObject, value);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set private field: " + fieldName, e);
        }
    }

    @Test
    public void shouldReturnEncryptedDataWhenCompletionsIsCalledSuccessfully() throws Exception {
        // Arrange
        CodeGenCompletionsParam codeGenCompletionsParam = new CodeGenCompletionsParam();
        codeGenCompletionsParam.setPrompt("test-prompt");
        codeGenCompletionsParam.setMax_completion_tokens(100);
        
        CodeGenCompletionsParam.Inputs inputs = new CodeGenCompletionsParam.Inputs();
        inputs.setFile_name("test-file.java");
        inputs.setLine_type("java");
        inputs.setSelectedCode("test code");
        codeGenCompletionsParam.setInputs(inputs);

        String requestId = "test-request-id";
        
        OrderVerifiedRes orderVerifiedRes = OrderVerifiedRes.builder()
                .token("test-token")
                .email("<EMAIL>")
                .team("test-team")
                .build();

        String pluginType = "test-plugin-type";
        String pluginVersion = "1.0.0";
        long startTime = System.currentTimeMillis();
        String userId = "test-user-id";

        CodeGenCompletionsRes.ChoicesBean choicesBean = new CodeGenCompletionsRes.ChoicesBean();
        choicesBean.setText("test completion");

        CodeGenCompletionsRes.Usage usage = new CodeGenCompletionsRes.Usage();
        usage.setCompletion_tokens(10);
        usage.setPrompt_tokens(20);
        usage.setTotal_tokens(30);

        CodeGenCompletionsRes codeGenCompletionsRes = new CodeGenCompletionsRes();
        codeGenCompletionsRes.setModel("test-model");
        codeGenCompletionsRes.setChoices(java.util.Collections.singletonList(choicesBean));
        codeGenCompletionsRes.setUsage(usage);

        when(codeClient.codeGenCompletions(any(CodeGenCompletionsParam.class), anyString(), anyString(), anyString(), anyString(), anyString()))
                .thenReturn(codeGenCompletionsRes);

        // Mock ZhanluAESUtils静态方法
        try (MockedStatic<ZhanluAESUtils> zhanluAESUtilsMockedStatic = Mockito.mockStatic(ZhanluAESUtils.class)) {
            zhanluAESUtilsMockedStatic.when(() -> ZhanluAESUtils.aesEncrypt(anyString(), anyString()))
                    .thenReturn("encrypted-data");

            // Act
            Map<String, String> result = codeServiceImpl.completions(codeGenCompletionsParam, requestId, orderVerifiedRes, pluginType, pluginVersion, startTime, userId);

            // Assert
            assertNotNull(result);
            assertTrue(result.containsKey("data"));
            assertEquals("encrypted-data", result.get("data"));

            // 验证方法调用
            verify(codeClient).codeGenCompletions(any(CodeGenCompletionsParam.class), anyString(), anyString(), anyString(), anyString(), anyString());
            verify(kafkaTemplate).send(anyString(), anyString());
        }
    }

    @Test
    public void shouldThrowExceptionWhenCompletionsFails() {
        // Arrange
        CodeGenCompletionsParam codeGenCompletionsParam = new CodeGenCompletionsParam();
        codeGenCompletionsParam.setPrompt("test-prompt");
        
        String requestId = "test-request-id";
        
        OrderVerifiedRes orderVerifiedRes = OrderVerifiedRes.builder()
                .token("test-token")
                .email("<EMAIL>")
                .team("test-team")
                .build();

        String pluginType = "test-plugin-type";
        String pluginVersion = "1.0.0";
        long startTime = System.currentTimeMillis();
        String userId = "test-user-id";

        when(codeClient.codeGenCompletions(any(CodeGenCompletionsParam.class), anyString(), anyString(), anyString(), anyString(), anyString()))
                .thenThrow(new RuntimeException("Test exception"));

        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            codeServiceImpl.completions(codeGenCompletionsParam, requestId, orderVerifiedRes, pluginType, pluginVersion, startTime, userId);
        });
    }

    @Test
    public void shouldGeneratePromptCorrectly() {
        // Arrange
        CodeGenCompletionsRes codeGenCompletionsRes = new CodeGenCompletionsRes();
        codeGenCompletionsRes.setModel("test-model");
        
        CodeGenCompletionsRes.ChoicesBean choicesBean = new CodeGenCompletionsRes.ChoicesBean();
        choicesBean.setText("test completion");
        codeGenCompletionsRes.setChoices(java.util.Collections.singletonList(choicesBean));
        
        CodeGenCompletionsRes.Usage usage = new CodeGenCompletionsRes.Usage();
        usage.setCompletion_tokens(10);
        usage.setPrompt_tokens(20);
        usage.setTotal_tokens(30);
        codeGenCompletionsRes.setUsage(usage);

        String requestId = "test-request-id";
        
        OrderVerifiedRes orderVerifiedRes = OrderVerifiedRes.builder()
                .token("test-token")
                .email("<EMAIL>")
                .team("test-team")
                .build();

        CodeGenCompletionsParam codeGenCompletionsParam = new CodeGenCompletionsParam();
        codeGenCompletionsParam.setPrompt("test-prompt");
        codeGenCompletionsParam.setMax_completion_tokens(100);
        
        CodeGenCompletionsParam.Inputs inputs = new CodeGenCompletionsParam.Inputs();
        inputs.setFile_name("test-file.java");
        inputs.setLine_type("java");
        inputs.setSelectedCode("test code");
        codeGenCompletionsParam.setInputs(inputs);

        String pluginType = "test-plugin-type";
        String pluginVersion = "1.0.0";
        String userId = "test-user-id";
        Boolean success = true;

        // Act
        codeServiceImpl.generatePrompt(codeGenCompletionsRes, requestId, orderVerifiedRes, codeGenCompletionsParam, pluginType, pluginVersion, userId, success);

        // Assert
        verify(kafkaTemplate).send(anyString(), anyString());
    }

    @Test
    public void shouldGenerateCodegenCorrectly() {
        // Arrange
        String requestId = "test-request-id";
        
        OrderVerifiedRes orderVerifiedRes = OrderVerifiedRes.builder()
                .token("test-token")
                .email("<EMAIL>")
                .team("test-team")
                .build();

        String pluginVersion = "1.0.0";
        String pluginType = "test-plugin-type";
        Boolean success = true;
        long costTime = 1000L;
        String exception = "";

        // Act
        codeServiceImpl.generateCogeGen(requestId, orderVerifiedRes, pluginVersion, pluginType, success, costTime, exception);

        // Assert
        verify(kafkaTemplate).send(anyString(), anyString());
    }

    @Test
    public void shouldGenerateRequestCorrectly() {
        // Arrange
        String requestId = "test-request-id";
        
        OrderVerifiedRes orderVerifiedRes = OrderVerifiedRes.builder()
                .token("test-token")
                .email("<EMAIL>")
                .team("test-team")
                .build();

        String userId = "test-user-id";
        String serviceType = ServiceType.Completions.getServiceType();
        String requestBody = "test-request-body";
        String answer = "test-answer";
        String messageId = "test-message-id";
        String sessionId = "test-session-id";
        Boolean success = true;
        long costTime = 1000L;

        // Act
        codeServiceImpl.generateRequest(requestId, orderVerifiedRes, userId, serviceType, requestBody, answer, messageId, sessionId, success, costTime);

        // Assert
        verify(kafkaTemplate).send(anyString(), anyString());
    }
}