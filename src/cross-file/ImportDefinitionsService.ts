import * as vscode from "vscode"
import { QueryMatch, Point } from "web-tree-sitter"
import { LRUCache } from "lru-cache"

import { getFullLanguageName, getParserForFile, getQueryForFile, IGNORE_PATH_PATTERNS } from "./util/treeSitter"
import { CrossFileInfo, RangeInFile, RangeInFileWithContents } from "./symbol.model"
import { gotoDefinition, readFile, readRangeInFile } from "./util/ide"
import { execAsync } from "../utils/git"
import { getWorkspacePath } from "../utils/path"

import { createTimeoutPromise } from "../utils/promise.util"
import { queryJavascriptDependentFiles } from "./languages"

export class ImportDefinitionsService {
	crossFileCache: LRUCache<string, CrossFileInfo, unknown> = new LRUCache<string, CrossFileInfo>({
		max: 10,
	})
	private fileDiffCache = new LRUCache<string, string>({
		max: 10,
	})

	constructor() {
		vscode.window.onDidChangeActiveTextEditor((textEditor: vscode.TextEditor | undefined) => {
			if (textEditor && textEditor.document.uri.fsPath) {
				this.getCrossFileInfo(textEditor.document.uri).catch((e) =>
					console.warn(`Failed to initialize ImportDefinitionService: ${e.message}`),
				)

				this.gitDiffHeadFile(textEditor.document.uri.fsPath).catch((e) =>
					console.warn(`Failed to initialize gitDiffHeadFile: ${e.message}`),
				)
			}
		})
	}

	async get(uri: vscode.Uri): Promise<CrossFileInfo | undefined> {
		setTimeout(() => {
			this.getCrossFileInfo(uri)
		}, 0)
		return this.crossFileCache.get(uri.fsPath)
	}

	async getUnitTest(uri: vscode.Uri): Promise<CrossFileInfo | undefined> {
		// 先检查缓存
		const cached = this.crossFileCache.get(uri.fsPath)
		if (cached) {
			return cached
		}

		// 如果缓存中没有，直接执行 getCrossFileInfo 并等待结果
		await this.getCrossFileInfo(uri)

		// 返回更新后的缓存结果
		return this.crossFileCache.get(uri.fsPath)
	}

	private async getCrossFileInfo(uri: vscode.Uri): Promise<any> {
		if (uri.fsPath.endsWith(".ipynb")) {
			// Commenting out this line was the solution to https://github.com/continuedev/continue/issues/1463
			return
		}

		const language = getFullLanguageName(uri.fsPath)

		if (language === "javascript") {
			return queryJavascriptDependentFiles(uri).then((res) => {
				if (res && Object.keys(res.imports).length) {
					this.crossFileCache.set(uri.fsPath, res)
				}
			})
		}

		const parser = await getParserForFile(uri.fsPath)
		if (!parser) {
			return
		}

		const fileContents = await readFile(uri)
		if (!fileContents.trim().length) {
			return
		}

		const ast = parser.parse(fileContents, undefined, {
			includedRanges: [
				{
					startIndex: 0,
					endIndex: 10_000,
					startPosition: { row: 0, column: 0 },
					endPosition: { row: 100, column: 0 },
				},
			],
		})

		if (!ast) {
			return
		}

		const query = await getQueryForFile(uri.fsPath)

		if (!query) {
			return
		}

		const matches: QueryMatch[] = query?.matches(ast.rootNode)

		const fileInfo: CrossFileInfo = {
			imports: {},
		}

		const workspaceFolder: vscode.WorkspaceFolder | undefined = vscode.workspace.getWorkspaceFolder(uri)
		const workspaceFolderName: string = workspaceFolder?.name || ""

		// 使用Set来避免重复处理相同的导入
		const processedImports = new Set<string>()

		// 对于Java文件，先收集所有通配符导入的包路径
		const wildcardImports = new Set<string>()
		if (language === "java") {
			for (const match of matches) {
				const importNode = match.captures[0].node
				// 检查是否为通配符导入（父节点包含asterisk）
				if (importNode.parent?.children.some((child) => child && child.type === "asterisk")) {
					const packagePath = this.getFullPackagePath(importNode)
					if (packagePath) {
						wildcardImports.add(packagePath)
					}
				}
			}
		}

		for (const match of matches) {
			const importText = match.captures[0].node.text

			// 跳过已处理的导入和过短的标识符
			if (processedImports.has(importText) || importText.length < 2) {
				continue
			}
			processedImports.add(importText)

			// 跳过Java基础类型和关键字
			if (language === "java" && this.isJavaBuiltinType(importText)) {
				continue
			}

			// 对于通配符导入的包名，跳过处理（我们只处理实际使用的类型）
			if (language === "java" && wildcardImports.has(importText)) {
				continue
			}

			const startPosition: Point = match.captures[0].node.startPosition
			let defs: RangeInFile[] = await gotoDefinition(uri, {
				line: startPosition.row,
				character: startPosition.column,
			})

			// 如果LSP查找失败，尝试备用方案
			if (defs.length === 0 && language === "java") {
				// 检查是否为通配符导入中实际使用的类型
				defs = await this.fallbackJavaDefinitionSearch(importText, workspaceFolder, wildcardImports)
			}

			const pureDefs: RangeInFile[] = defs.filter((def) => {
				const isIgnoredPath =
					uri.fsPath === def.filepath ||
					IGNORE_PATH_PATTERNS[language]?.some((pattern) => pattern.test(def.filepath))

				// 对于Java，放宽工作空间限制，允许项目内的其他模块
				if (language === "java") {
					return (
						!isIgnoredPath &&
						(def.uri?.path.includes(workspaceFolderName) ||
							this.isProjectRelatedPath(def.filepath, workspaceFolder?.uri.fsPath))
					)
				}

				return !isIgnoredPath && def.uri?.path.includes(workspaceFolderName)
			})

			if (pureDefs.length) {
				// 对于所有语言（包括Java），使用统一的方式读取文件内容
				fileInfo.imports[match.captures[0].node.text] = await Promise.all(
					pureDefs.map(async (def) => ({
						...def,
						contents: await readRangeInFile(def.filepath, def.range),
					})),
				)
			}
		}

		if (Object.keys(fileInfo.imports).length) {
			this.crossFileCache.set(uri.fsPath, fileInfo)
		}
	}

	/**
	 * 检查是否为Java内置类型或关键字
	 * @param text 要检查的文本
	 * @returns 是否为内置类型
	 */
	private isJavaBuiltinType(text: string): boolean {
		const javaBuiltinTypes = new Set([
			// 基础数据类型
			"boolean",
			"byte",
			"char",
			"short",
			"int",
			"long",
			"float",
			"double",
			"void",
			// 常用包装类型
			"Boolean",
			"Byte",
			"Character",
			"Short",
			"Integer",
			"Long",
			"Float",
			"Double",
			"String",
			"Object",
			"Class",
			// 常用集合类型
			"List",
			"Set",
			"Map",
			"Collection",
			"ArrayList",
			"HashMap",
			"HashSet",
			// 关键字
			"public",
			"private",
			"protected",
			"static",
			"final",
			"abstract",
			"synchronized",
			"native",
			"strictfp",
			"transient",
			"volatile",
			"class",
			"interface",
			"enum",
			"extends",
			"implements",
			"import",
			"package",
			"if",
			"else",
			"for",
			"while",
			"do",
			"switch",
			"case",
			"default",
			"break",
			"continue",
			"return",
			"try",
			"catch",
			"finally",
			"throw",
			"throws",
			"new",
			"this",
			"super",
			"null",
			"true",
			"false",
			"instanceof",
			"assert",
			// 常用注解
			"Override",
			"Deprecated",
			"SuppressWarnings",
			"FunctionalInterface",
			"SafeVarargs",
			"Retention",
			"Target",
			"Documented",
			"Inherited",
			// 单字符或过短的标识符
		])

		// 检查是否为内置类型
		if (javaBuiltinTypes.has(text)) {
			return true
		}

		// 检查是否为单字符或过短的标识符
		if (text.length <= 1) {
			return true
		}

		// 检查是否为数字
		if (/^\d+$/.test(text)) {
			return true
		}

		return false
	}

	/**
	 * 获取导入节点的完整包路径
	 * @param importNode Tree-sitter节点
	 * @returns 完整的包路径，如 "com.example.config"
	 */
	private getFullPackagePath(importNode: any): string | null {
		try {
			// 向上遍历找到import_declaration节点
			let current = importNode
			while (current && current.type !== "import_declaration") {
				current = current.parent
			}

			if (!current) {
				return null
			}

			// 查找scoped_identifier节点
			const scopedIdentifier = current.children.find((child: any) => child && child.type === "scoped_identifier")

			if (scopedIdentifier) {
				return scopedIdentifier.text
			}

			return null
		} catch (error) {
			console.warn("Failed to get full package path:", error)
			return null
		}
	}

	/**
	 * Java定义查找的备用方案
	 * 当LSP无法找到定义时，尝试通过文件系统搜索
	 * @param importText 导入的类名
	 * @param workspaceFolder 工作空间文件夹
	 * @param wildcardImports 通配符导入的包路径集合
	 * @returns 找到的定义文件列表
	 */
	private async fallbackJavaDefinitionSearch(
		importText: string,
		workspaceFolder?: vscode.WorkspaceFolder,
		wildcardImports?: Set<string>,
	): Promise<RangeInFile[]> {
		if (!workspaceFolder) {
			return []
		}

		try {
			// 提取类名（去掉包名）
			const className = importText.split(".").pop()
			if (!className) {
				return []
			}

			// 检查是否为通配符导入中的类型
			let searchPattern = `**/${className}.java`

			// 如果有通配符导入，优先在对应包路径下搜索
			if (wildcardImports && wildcardImports.size > 0) {
				for (const packagePath of wildcardImports) {
					const packageDir = packagePath.replace(/\./g, "/")
					const specificPattern = `**/${packageDir}/${className}.java`

					const specificFiles = await vscode.workspace.findFiles(
						new vscode.RelativePattern(workspaceFolder, specificPattern),
						"**/target/**",
						5,
					)

					if (specificFiles.length > 0) {
						// 找到了通配符导入包中的类，直接返回
						return await this.validateAndCreateRangeInFiles(specificFiles, className)
					}
				}
			}

			// 如果在通配符导入包中没找到，进行全局搜索
			const files = await vscode.workspace.findFiles(
				new vscode.RelativePattern(workspaceFolder, searchPattern),
				"**/target/**", // 排除编译输出目录
				10, // 最多找10个文件
			)

			return await this.validateAndCreateRangeInFiles(files, className)
		} catch (error) {
			console.warn(`Failed to perform fallback search for ${importText}:`, error)
			return []
		}
	}

	/**
	 * 验证文件并创建RangeInFile对象
	 * @param files 文件URI数组
	 * @param className 类名
	 * @returns RangeInFile数组
	 */
	private async validateAndCreateRangeInFiles(files: vscode.Uri[], className: string): Promise<RangeInFile[]> {
		try {
			const results: RangeInFile[] = []
			for (const file of files) {
				// 验证文件内容是否包含对应的类定义
				const content = await readFile(file)
				if (
					content.includes(`class ${className}`) ||
					content.includes(`interface ${className}`) ||
					content.includes(`enum ${className}`)
				) {
					results.push({
						filepath: file.fsPath,
						uri: file,
						range: {
							start: { line: 0, character: 0 },
							end: { line: 0, character: 0 },
						},
					})
				}
			}

			return results
		} catch (error) {
			console.warn(`Failed to validate files for ${className}:`, error)
			return []
		}
	}

	/**
	 * 判断文件路径是否与项目相关
	 * @param filePath 文件路径
	 * @param workspacePath 工作空间路径
	 * @returns 是否为项目相关路径
	 */
	private isProjectRelatedPath(filePath: string, workspacePath?: string): boolean {
		if (!workspacePath) {
			return false
		}

		// 检查是否在工作空间目录下
		if (filePath.startsWith(workspacePath)) {
			return true
		}

		// 检查是否是同一项目的其他模块（通过共同的父目录判断）
		const workspaceParent = workspacePath.split("/").slice(0, -1).join("/")
		if (filePath.startsWith(workspaceParent)) {
			// 检查是否包含Java项目的特征文件
			return (
				filePath.includes("/src/main/java/") ||
				filePath.includes("/src/test/java/") ||
				filePath.includes("pom.xml") ||
				filePath.includes("build.gradle")
			)
		}

		return false
	}

	private gitDiffHeadFile(fsPath: string): Promise<string> {
		// const theDiffStartTme = Date.now()
		return execAsync(`git diff HEAD -- ${fsPath}`, { cwd: getWorkspacePath() }).then(
			(res: { stdout: string; stderr: string }) => {
				// console.log(11122, res, )
				this.fileDiffCache.set(fsPath, res.stdout)
				return res.stdout
			},
		)
	}

	gitDiffHeadFileTimeout(fsPath: string, timeout = 0): Promise<any> {
		return Promise.race([
			this.gitDiffHeadFile(fsPath),
			createTimeoutPromise(timeout, this.fileDiffCache.get(fsPath) ?? ""),
		])
	}
}
