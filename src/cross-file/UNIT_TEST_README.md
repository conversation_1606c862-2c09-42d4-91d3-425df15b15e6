# 跨文件感知功能

## 功能概述

在单测智能体模式下，`read_file` 工具执行过程中会自动追加跨文件感知逻辑，帮助模型更好地理解代码的上下文。本功能经过增强改进，解决了LSP定义查找限制的问题，显著提升了跨文件感知功能的覆盖率和准确性。

## 功能特性

1. **自动跨文件感知**: 在单测智能体模式下，读取文件时自动分析该文件中所有import的自定义类文件
2. **类成员属性包含**: 跨文件感知内容类似 `list_code_definition_names` 的实现逻辑，但增加了类成员属性的内容
3. **历史会话缓存**: 最多保留历史会话中最近3次跨文件感知内容，避免context超长问题
4. **智能缓存机制**: 最多20条跨文件信息，避免重复处理
5. **备用定义查找**: 当LSP无法找到定义时，使用基于文件系统的备用搜索机制
6. **智能项目路径识别**: 支持多模块项目，识别同一项目的不同模块
7. **增强的导入解析**: 支持更多类型的Java引用，包括静态导入、泛型、注解等
8. **内置类型过滤**: 自动过滤Java内置类型和第三方库，减少噪音

## 输出格式

跨文件感知内容以以下格式追加到 `read_file` 的结果之后：

```
[list_cross_code_definition_names for 'File path here'] Result:
# FileName.java
1--10 | package com.example;
11--20 | public class ExampleClass {
21--25 |     private String field;
26--30 |     public void method() {}
31--31 | }
```

## 使用场景

- **单元测试生成**: 在生成单元测试时，模型可以了解被测试类的依赖关系
- **代码理解**: 帮助模型理解复杂的类继承和依赖关系
- **重构支持**: 在重构代码时提供完整的上下文信息

## 技术实现

### 核心组件

1. **ImportDefinitionsService**: 负责生成跨文件感知内容
    - `generateCrossFileDefinitions()`: 生成跨文件定义内容
    - `generateDefinitionsWithMembers()`: 生成包含成员属性的定义
    - `formatJavaDefinitions()`: 格式化Java类定义

2. **readFileTool**: 集成跨文件感知逻辑
    - 仅在单测智能体模式 (`mode === "test"`) 下生效
    - 自动调用 ImportDefinitionsService 生成跨文件内容

3. **Task类**: 管理历史会话缓存
    - `addCrossFileDefinitionsToHistory()`: 添加到历史缓存
    - `getCrossFileDefinitionsHistory()`: 获取历史内容
    - `cleanupCrossFileDefinitionsHistory()`: 清理重复项

### 增强功能实现

#### 1. 扩展的Java忽略模式

新增了针对Java项目的详细忽略模式，过滤掉不相关的第三方库和系统文件：

```typescript
[LanguageName.JAVA]: [
    /.*\/target\/classes\/.*/, // Maven编译输出
    /.*\/build\/classes\/.*/, // Gradle编译输出
    /.*\.jar!\/.*/, // JAR包内部文件
    /.*\/\.m2\/repository\/.*/, // Maven本地仓库
    /.*\/\.gradle\/caches\/.*/, // Gradle缓存
    /.*\/src\/test\/.*/, // 测试文件
    /java\/lang\/.*/, // Java标准库
    /java\/util\/.*/, // Java工具类
    /javax\/.*/, // Java扩展库
    /org\/springframework\/.*/, // Spring框架
    /org\/apache\/.*/, // Apache库
    /com\/fasterxml\/jackson\/.*/, // Jackson库
    /lombok\/.*/, // Lombok库
]
```

#### 2. 备用定义查找机制

当LSP无法找到定义时，实现了基于文件系统的备用搜索：

- 提取类名并在工作空间中搜索对应的Java文件
- 验证文件内容是否包含对应的类定义
- 支持类、接口、枚举的识别

#### 3. 智能项目路径识别

放宽了工作空间限制，支持多模块项目：

- 识别同一项目的不同模块
- 通过共同父目录和项目特征文件判断关联性
- 支持Maven和Gradle项目结构

#### 4. 增强的导入解析

扩展了Java导入查询，支持更多类型的引用：

- 标准导入语句
- 静态导入语句
- 通配符导入
- 类型注解中的类引用
- 方法参数和返回类型
- 泛型类型参数
- 继承和实现中的类引用
- 注解中的类引用
- 异常声明中的类引用

#### 5. 内置类型过滤

添加了Java内置类型和关键字的识别，避免处理不必要的引用：

- 基础数据类型（int, boolean, String等）
- 常用集合类型（List, Map, Set等）
- Java关键字和注解
- 过短的标识符和数字

#### 6. 重复处理优化

使用Set来避免重复处理相同的导入，提高性能。

#### 7. 详细的日志记录

添加了详细的调试日志，便于问题诊断：

- 缓存使用情况
- 导入发现统计
- 成功/失败的定义生成
- 错误详情记录

### 缓存策略

- **文件级缓存**: ImportDefinitionsService 内部缓存，最多20条记录
- **历史会话缓存**: Task 级别缓存，最多保留最近3次跨文件感知内容
- **重复项清理**: 自动清理历史记录中的重复内容

## 预期效果

通过这些改进，跨文件感知功能应该能够：

1. **发现更多相关类**：从原来的5个增加到可能的15-20个
2. **提高查找成功率**：通过备用搜索机制减少LSP查找失败的影响
3. **支持复杂项目结构**：更好地处理多模块Maven/Gradle项目
4. **减少噪音**：过滤掉不相关的第三方库和系统类
5. **提升性能**：避免重复处理和不必要的查找

## 使用示例

在单测智能体模式下读取Java文件时，系统会自动：

1. 解析文件中的所有类型引用
2. 使用LSP查找定义，失败时使用备用搜索
3. 过滤掉第三方库和系统类
4. 生成项目内相关类的定义信息
5. 缓存结果以提高后续访问性能

## 测试验证

可以通过以下方式验证改进效果：

1. 运行增强测试套件：`npm test -- ImportDefinitionsService.enhanced.test.ts`
2. 在单测模式下读取复杂的Java控制器文件
3. 检查生成的跨文件感知内容数量和质量
4. 观察控制台日志了解处理详情

## 配置说明

功能自动在单测智能体模式下启用，无需额外配置。

## 注意事项

1. 仅在单测智能体模式下生效
2. 仅处理单个文件的 `read_file` 请求
3. 最多处理20条跨文件信息，避免context过长
4. 历史缓存最多保留3次，平衡context长度和信息完整性
5. 备用搜索机制会增加一定的处理时间，但通过缓存可以减少重复开销
6. 项目路径识别基于常见的Java项目结构，可能需要根据特殊项目调整
7. 日志记录在生产环境中可能需要调整级别
