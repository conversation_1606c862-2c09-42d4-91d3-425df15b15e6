# 跨文件代码补全功能架构文档

## 系统架构概览

跨文件代码补全功能采用分层架构设计，主要包括以下几个层次：

1. **接口层**: 提供VS Code扩展API和工具接口
2. **服务层**: 核心业务逻辑和服务组件
3. **解析层**: 语法分析和AST处理
4. **存储层**: 缓存和数据管理
5. **工具层**: 辅助工具和实用函数

## 核心组件架构

### 1. ImportDefinitionsService (核心服务)

```
ImportDefinitionsService
├── crossFileCache (LRU缓存)
├── get() - 获取跨文件信息
├── getCrossFileInfo() - 分析文件依赖
├── generateCrossFileDefinitions() - 生成定义内容
├── generateDefinitionsWithMembers() - 生成成员定义
├── formatJavaDefinitions() - 格式化Java定义
├── isJavaBuiltinType() - 内置类型判断
├── isProjectRelatedPath() - 项目路径判断
├── findFileByClassName() - 文件搜索
└── containsClassDefinition() - 类定义验证
```

**职责**:

- 管理跨文件依赖分析的核心逻辑
- 提供缓存机制优化性能
- 集成LSP和备用搜索机制

### 2. RootPathContextService (上下文服务)

```
RootPathContextService
├── cache (上下文缓存)
├── importDefinitionsService (依赖注入)
├── get() - 获取上下文信息
├── getSnippets() - 获取代码片段
├── getContextForPath() - 获取路径上下文
└── 各种语言特定的查询方法
```

**职责**:

- 基于AST路径提供上下文信息
- 管理代码片段的获取和缓存
- 支持多语言的上下文分析

### 3. CrossFileDefinitionsService (独立工具服务)

```
CrossFileDefinitionsService
├── importDefinitionsService (依赖注入)
├── crossFileDefinitionsCache (工具缓存)
├── generateCrossFileDefinitions() - 生成跨文件定义
└── generateDefinitionsWithMembers() - 生成成员定义
```

**职责**:

- 提供独立的跨文件定义工具
- 管理工具级别的缓存
- 支持手动调用和自动集成

## 数据流架构

### 1. 自动触发流程

```
用户操作 (read_file)
    ↓
readFileTool 检测单测模式
    ↓
调用 CrossFileDefinitionsService
    ↓
ImportDefinitionsService.get()
    ↓
语言识别 → AST解析 → 导入提取
    ↓
LSP查询 → 备用搜索 → 结果合并
    ↓
缓存存储 → 格式化输出
    ↓
返回跨文件感知内容
```

### 2. 手动触发流程

```
用户调用工具 (list_cross_code_definition_names)
    ↓
工具分发器识别调用
    ↓
CrossFileDefinitionsService.generateCrossFileDefinitions()
    ↓
ImportDefinitionsService 处理
    ↓
返回格式化的定义内容
```

### 3. 代码补全流程

```
光标位置变化
    ↓
RootPathContextService.get()
    ↓
AST路径分析
    ↓
上下文片段获取
    ↓
跨文件信息补充
    ↓
返回补全建议
```

## 语言支持架构

### 1. 语言配置系统

```
languages.extension.ts
├── CodeLanguageDetails 接口
├── supportedLanguages 配置
├── 文件扩展名映射
├── 语法注释配置
├── 忽略规则配置
└── 语言特定函数
```

### 2. 查询系统

```
languages/
├── index.ts (导出接口)
├── javascript.ts
│   ├── queryJavascriptDefinition()
│   └── queryJavascriptDependentFiles()
├── java.ts
│   └── queryJavaClassDefinition()
└── 其他语言实现...
```

### 3. Tree-sitter集成

```
util/treeSitter.ts
├── 语言解析器管理
├── 查询规则定义
├── AST节点处理
├── 符号提取逻辑
└── 忽略模式配置
```

## 缓存架构

### 1. 多层缓存设计

```
缓存层次结构:
├── LSP缓存 (executeGotoProvider)
│   ├── 大小: 500条
│   ├── 策略: LRU
│   └── 用途: LSP查询结果
├── 服务缓存 (ImportDefinitionsService)
│   ├── 大小: 20条
│   ├── TTL: 10分钟
│   └── 用途: 跨文件信息
├── 工具缓存 (CrossFileDefinitionsService)
│   ├── 大小: 20条
│   ├── 策略: LRU
│   └── 用途: 工具调用结果
└── 历史缓存 (Task级别)
    ├── 大小: 3次会话
    ├── 策略: FIFO
    └── 用途: 会话历史
```

### 2. 缓存键策略

```typescript
// LSP缓存键
const lspKey = `${providerName}${uri}${line}${character}`

// 文件缓存键
const fileKey = uri.fsPath

// 工具缓存键
const toolKey = filePath
```

## 错误处理架构

### 1. 异常分类

```
错误类型:
├── LSP错误
│   ├── 提供者不可用
│   ├── 查询超时
│   └── 结果解析失败
├── 文件系统错误
│   ├── 文件不存在
│   ├── 权限不足
│   └── 读取失败
├── 解析错误
│   ├── AST解析失败
│   ├── 语言不支持
│   └── 语法错误
└── 缓存错误
    ├── 内存不足
    ├── 序列化失败
    └── 过期清理失败
```

### 2. 容错机制

```typescript
// 多级降级策略
try {
	// 1. 尝试LSP查询
	result = await lspQuery()
} catch (lspError) {
	try {
		// 2. 尝试备用搜索
		result = await fallbackSearch()
	} catch (fallbackError) {
		// 3. 返回空结果
		result = []
		console.warn("All methods failed", { lspError, fallbackError })
	}
}
```

## 性能优化架构

### 1. 并发控制

```typescript
// Promise池管理
const concurrencyLimit = 5
const promisePool = new Map<string, Promise<any>>()

// 防抖机制
@debounce(300)
async function processFile(uri: vscode.Uri) {
    // 处理逻辑...
}
```

### 2. 内存管理

```typescript
// 定期清理
setInterval(() => {
	cache.prune() // 清理过期缓存
	gc() // 垃圾回收
}, 60000) // 每分钟执行一次
```

### 3. 懒加载

```typescript
// 按需加载语言解析器
async function getParserForFile(filepath: string) {
	const language = getLanguageForFile(filepath)
	if (!parsers.has(language)) {
		parsers.set(language, await loadParser(language))
	}
	return parsers.get(language)
}
```

## 扩展架构

### 1. 插件化设计

```
扩展点:
├── 语言支持扩展
│   ├── 新增语言配置
│   ├── 实现查询规则
│   └── 添加解析逻辑
├── 缓存策略扩展
│   ├── 自定义缓存实现
│   ├── 分布式缓存支持
│   └── 持久化存储
├── 搜索策略扩展
│   ├── 外部索引集成
│   ├── 语义搜索支持
│   └── 机器学习辅助
└── 输出格式扩展
    ├── 自定义格式化器
    ├── 多种输出格式
    └── 模板系统
```

### 2. 配置系统

```typescript
interface CrossFileConfig {
	cacheSize: number
	cacheTTL: number
	concurrencyLimit: number
	enableFallbackSearch: boolean
	ignorePatterns: RegExp[]
	supportedLanguages: string[]
}
```

## 监控和调试架构

### 1. 日志系统

```
日志级别:
├── DEBUG: 详细的处理过程
├── INFO: 关键操作信息
├── WARN: 警告和降级处理
└── ERROR: 错误和异常
```

### 2. 性能监控

```typescript
// 性能指标收集
interface PerformanceMetrics {
	cacheHitRate: number
	averageProcessingTime: number
	errorRate: number
	memoryUsage: number
}
```

### 3. 调试工具

```typescript
// 调试接口
interface DebugInterface {
	dumpCache(): CacheSnapshot
	analyzePerformance(): PerformanceReport
	validateConfiguration(): ValidationResult
	traceExecution(filePath: string): ExecutionTrace
}
```

## 安全架构

### 1. 输入验证

```typescript
// 路径安全检查
function validatePath(path: string): boolean {
	// 防止路径遍历攻击
	return !path.includes("..") && !path.startsWith("/") && path.length < 1000
}
```

### 2. 资源限制

```typescript
// 资源使用限制
const limits = {
	maxFileSize: 10 * 1024 * 1024, // 10MB
	maxCacheSize: 100 * 1024 * 1024, // 100MB
	maxProcessingTime: 30000, // 30秒
}
```

## 总结

跨文件代码补全功能的架构设计遵循以下原则：

1. **模块化**: 清晰的组件分离和职责划分
2. **可扩展**: 支持新语言和功能的灵活扩展
3. **高性能**: 多层缓存和并发优化
4. **容错性**: 完善的错误处理和降级机制
5. **可维护**: 良好的代码组织和文档支持

这种架构设计确保了系统的稳定性、性能和可扩展性，为用户提供了可靠的跨文件代码补全体验。
