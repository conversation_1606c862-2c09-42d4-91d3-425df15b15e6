[read_file for 'acepilot-server-data-processor/acepilot-server-data-consumer/src/main/java/com/chinamobile/cmss/acepilot/server/data/controller/DataController.java'] Results:
<file><path>acepilot-server-data-processor/acepilot-server-data-consumer/src/main/java/com/chinamobile/cmss/acepilot/server/data/controller/DataController.java</path>
<content lines=1-82>
   1 | package com.chinamobile.cmss.acepilot.server.data.controller;
   2 |
   3 | import com.chinamobile.cmss.acepilot.server.common.model.AcepilotServerCode;
   4 | import com.chinamobile.cmss.acepilot.server.common.model.AcepilotServerException;
   5 | import com.chinamobile.cmss.acepilot.server.common.model.BaseResult;
   6 | import com.chinamobile.cmss.acepilot.server.data.model.CodeResultModel;
   7 | import com.chinamobile.cmss.acepilot.server.data.model.UserModel;
   8 | import com.chinamobile.cmss.acepilot.server.data.service.DataService;
   9 | import com.chinamobile.cmss.acepilot.server.data.utils.JsonUtils;
  10 | import lombok.extern.slf4j.Slf4j;
  11 | import org.springframework.beans.factory.annotation.Autowired;
  12 | import org.springframework.beans.factory.annotation.Value;
  13 | import org.springframework.web.bind.annotation.PostMapping;
  14 | import org.springframework.web.bind.annotation.RequestBody;
  15 | import org.springframework.web.bind.annotation.RequestMapping;
  16 | import org.springframework.web.bind.annotation.RestController;
  17 |
  18 | import java.text.ParseException;
  19 | import java.text.SimpleDateFormat;
  20 | import java.util.Date;
  21 |
  22 | @Slf4j
  23 | @RestController
  24 | @RequestMapping("/data")
  25 | public class DataController {
  26 |     @Autowired
  27 |     private DataService dataService;
  28 |
  29 |     @Value("${code-result.send.create.start}")
  30 |     private String startCreateTimeStr;
  31 |
  32 |     @Value("${code-result.send.create.end}")
  33 |     private String endCreateTimeStr;
  34 |
  35 |     @Value("${code-result.send.insert.start}")
  36 |     private String startInsertTimeStr;
  37 |
  38 |     @Value("${code-result.send.insert.end}")
  39 |     private String endInsertTimeStr;
  40 |
  41 |     @PostMapping("/compare/result")
  42 |     public BaseResult userSave(@RequestBody CodeResultModel codeResultModel) {
  43 |         log.info("compare success, result:{}", JsonUtils.toJson(codeResultModel));
  44 |         dataService.compareResult(codeResultModel);
  45 |         return BaseResult.success(null);
  46 |     }
  47 |
  48 |     @PostMapping("/send/result")
  49 |     public BaseResult sendCodeResults() {
  50 |         SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss"); // 定义时间格式
  51 |
  52 |         // 解析配置文件中的日期
  53 |         Date startCreateTimeDate;
  54 |         Date endCreateTimeDate;
  55 |         Date startInsertTimeDate;
  56 |         Date endInsertTimeDate;
  57 |         try {
  58 |             startCreateTimeDate = sdf.parse(startCreateTimeStr);
  59 |             endCreateTimeDate = sdf.parse(endCreateTimeStr);
  60 |             startInsertTimeDate = sdf.parse(startInsertTimeStr);
  61 |             endInsertTimeDate = sdf.parse(endInsertTimeStr);
  62 |         } catch (ParseException e) {
  63 |             log.info("Invalid date format in application.yml", e);
  64 |             throw new AcepilotServerException(AcepilotServerCode.INTERNAL_ERROR);
  65 |         }
  66 |
  67 |         dataService.sendResult(startCreateTimeDate, endCreateTimeDate, startInsertTimeDate, endInsertTimeDate);
  68 |         return BaseResult.success(null);
  69 |     }
  70 |
  71 |     @PostMapping("/deleteErrorCodeResults")
  72 |     public BaseResult deleteError() {
  73 |         dataService.deleteErrorCodeResults();
  74 |         return BaseResult.success(null);
  75 |     }
  76 |
  77 |     @PostMapping("/deleteErrorDiffs")
  78 |     public BaseResult delete() {
  79 |         dataService.deleteErrorDiffs();
  80 |         return BaseResult.success(null);
  81 |     }
  82 | }
</content>
</file>
[list_cross_code_definition_names for 'acepilot-server-data-processor/acepilot-server-data-consumer/src/main/java/com/chinamobile/cmss/acepilot/server/data/model/CodeResultModel.java'] Result:
# CodeResultModel.java
8--63 | @Getter
1--63 | package com.chinamobile.cmss.acepilot.server.data.model;
13--63 | public class CodeResultModel {
14--63 |     @JsonProperty("source")
15--63 |     private String source;@JsonProperty("authorEmail")
16--63 |     private String authorEmail;@JsonProperty("commitSHA")
17--63 |     private String commitSHA;@JsonProperty("revisionId")
18--63 |     private String revisionId;@JsonProperty("createTime")
19--63 |     private String createTime;@JsonProperty("insertTime")
20--63 |     private String insertTime;@JsonProperty("diffList")
21--63 |     private List<Diff> diffList;
35--62 |     @Getter
 
[list_cross_code_definition_names for 'acepilot-server-common\src\main\java\com\chinamobile\cmss\acepilot\server\common\model\BaseResult.java'] Result:
# BaseResult.java
9--62 | @Getter
1--62 | package com.chinamobile.cmss.acepilot.server.common.model;
25--31 |     public BaseResult(T body) {
14--62 |         public class BaseResult<T> {
15--62 |         static String KEY_TRACE = "X-B3-TraceId";
16--62 |         private static String CODE_SUCCESS = "Success";
17--62 |         private static String MSG_SUCCESS = "操作成功";
18--62 |         public static String STATE_OK = "OK";
19--62 |         public static String STATE_ERROR = "ERROR";
20--62 |         private String errorCode;
21--62 |         private String errorMessage;
22--62 |         private String requestId;
23--62 |         private String state;
24--62 |         private T body;
 
33--38 |     public BaseResult(BaseCode code) {
40--45 |     public BaseResult(String errorCode, String errorMessage) {