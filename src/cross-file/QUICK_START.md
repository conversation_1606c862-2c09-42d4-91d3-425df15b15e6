# 跨文件代码补全功能快速开始指南

## 概述

跨文件代码补全功能帮助AI模型理解代码的依赖关系，提供更准确的代码补全和分析。本指南将帮助您快速了解和使用这个功能。

## 5分钟快速体验

### 1. 启用单测智能体模式

在湛卢插件中，跨文件感知功能主要在单测智能体模式下自动启用。

1. 打开VS Code
2. 确保湛卢插件已安装并激活
3. 切换到单测智能体模式

### 2. 使用 read_file 工具

在单测模式下，使用 `read_file` 工具读取任何Java文件：

```xml
<read_file>
<path>src/main/java/com/example/controller/UserController.java</path>
</read_file>
```

您将看到类似以下的输出：

```
[read_file for 'src/main/java/com/example/controller/UserController.java'] Results:
<file><path>src/main/java/com/example/controller/UserController.java</path>
<content lines=1-50>
// 原始文件内容...
</content>
</file>

[list_cross_code_definition_names for 'src/main/java/com/example/model/User.java'] Result:
# User.java
1--10 | package com.example.model;
11--20 | public class User {
21--25 |     private String name;
26--30 |     public String getName() { return name; }
31--31 | }

[list_cross_code_definition_names for 'src/main/java/com/example/service/UserService.java'] Result:
# UserService.java
1--15 | package com.example.service;
16--25 | public class UserService {
26--35 |     public User findById(Long id) { ... }
36--36 | }
```

### 3. 手动调用跨文件工具

您也可以直接使用跨文件定义工具：

```xml
<list_cross_code_definition_names>
<path>src/main/java/com/example/controller/UserController.java</path>
</list_cross_code_definition_names>
```

## 支持的语言和项目类型

### 完全支持

- **Java**: Maven、Gradle项目
- **JavaScript/TypeScript**: Node.js、React、Vue项目

### 基础支持

- **Python**: 标准Python项目
- **C/C++**: 标准C/C++项目
- **Go**: Go模块项目

## 常见使用场景

### 1. 单元测试生成

当您需要为某个类生成单元测试时：

```xml
<read_file>
<path>src/main/java/com/example/service/OrderService.java</path>
</read_file>
```

系统会自动分析 `OrderService` 的依赖，包括：

- 依赖的模型类（如 `Order`, `User`）
- 使用的工具类（如 `DateUtils`, `ValidationUtils`）
- 注入的服务类（如 `PaymentService`, `NotificationService`）

### 2. 代码理解和重构

在理解复杂代码结构时：

```xml
<read_file>
<path>src/main/java/com/example/controller/ComplexController.java</path>
</read_file>
```

系统会提供相关类的定义信息，帮助您理解：

- 数据模型的结构
- 服务层的接口
- 工具类的功能

### 3. API文档生成

为REST API生成文档时：

```xml
<list_cross_code_definition_names>
<path>src/main/java/com/example/controller/ApiController.java</path>
</list_cross_code_definition_names>
```

系统会分析API使用的所有数据传输对象（DTO）和响应模型。

## 配置和自定义

### 1. 调整缓存大小

如果您的项目很大，可能需要调整缓存配置：

```typescript
// 在 ImportDefinitionsService 中
private crossFileCache = new LRUCache<string, CrossFileInfo>({
    max: 50, // 增加到50条记录
    ttl: 1000 * 60 * 15, // 延长到15分钟
})
```

### 2. 自定义忽略模式

为您的项目添加特定的忽略规则：

```typescript
const customIgnorePatterns = [
	/.*\/generated\/.*/, // 忽略生成的代码
	/.*\/test\/.*/, // 忽略测试代码
	/.*\/vendor\/.*/, // 忽略第三方库
]
```

### 3. 语言特定配置

为特定语言添加配置：

```typescript
// 在 languages.extension.ts 中
myLanguage: {
    fileExtensions: [".mylang"],
    syntaxComments: { start: "//", end: "" },
    excludedFolder: "\\build",
}
```

## 性能优化建议

### 1. 项目结构优化

- 保持清晰的模块分离
- 避免循环依赖
- 使用合理的包结构

### 2. 导入语句优化

- 避免过度使用通配符导入
- 及时清理未使用的导入
- 使用具体的类导入而非包导入

### 3. 缓存友好的开发习惯

- 避免频繁修改核心依赖文件
- 使用稳定的文件命名规范
- 保持一致的代码结构

## 故障排除

### 问题1: 无法找到某些类的定义

**症状**: 跨文件分析中缺少某些预期的类

**解决方案**:

1. 检查LSP服务是否正常运行
2. 确认文件路径正确
3. 检查是否被忽略模式过滤

```typescript
// 检查忽略模式
console.log("Ignored patterns:", IGNORE_PATH_PATTERNS[language])
```

### 问题2: 性能较慢

**症状**: 跨文件分析耗时较长

**解决方案**:

1. 检查缓存命中率
2. 调整并发限制
3. 优化忽略模式

```typescript
// 启用性能监控
console.time("cross-file-analysis")
await service.generateCrossFileDefinitions(filePath)
console.timeEnd("cross-file-analysis")
```

### 问题3: 缓存问题

**症状**: 修改文件后信息未更新

**解决方案**:

1. 清理缓存
2. 重启VS Code
3. 检查缓存TTL设置

```typescript
// 手动清理缓存
service.clearCache()
```

## 调试技巧

### 1. 启用详细日志

```typescript
// 在开发者控制台中查看日志
console.debug("Cross file analysis started for:", filePath)
```

### 2. 检查AST结构

```typescript
// 查看AST解析结果
const ast = await getAst(filepath, contents)
console.log("AST root node:", ast.rootNode)
```

### 3. 验证LSP响应

```typescript
// 测试LSP查询
const definitions = await executeGotoProvider({
	uri,
	line: 10,
	character: 5,
	name: "vscode.executeDefinitionProvider",
})
console.log("LSP definitions:", definitions)
```

## 最佳实践

### 1. 代码组织

```java
// 好的实践：清晰的导入
import com.example.model.User;
import com.example.service.UserService;
import com.example.util.ValidationUtils;

// 避免：过度的通配符导入
import com.example.model.*;
import com.example.service.*;
```

### 2. 项目结构

```
src/
├── main/
│   ├── java/
│   │   ├── controller/    # 控制器层
│   │   ├── service/       # 服务层
│   │   ├── model/         # 数据模型
│   │   └── util/          # 工具类
│   └── resources/
└── test/
    └── java/
```

### 3. 依赖管理

- 使用依赖注入框架（如Spring）
- 避免静态依赖
- 保持接口和实现分离

## 进阶用法

### 1. 自定义语言支持

```typescript
// 添加新语言支持
export const myLanguageQueries = {
	importQuery: `
        (import_statement 
          source: (string) @import.source)
    `,
	classQuery: `
        (class_declaration 
          name: (identifier) @class.name)
    `,
}
```

### 2. 集成外部工具

```typescript
// 集成静态分析工具
async function enhancedAnalysis(filePath: string) {
	const crossFileInfo = await service.get(uri)
	const staticAnalysis = await runStaticAnalyzer(filePath)
	return mergeResults(crossFileInfo, staticAnalysis)
}
```

### 3. 自定义输出格式

```typescript
// 自定义格式化器
function customFormatter(definitions: SymbolWithRange[]): string {
	return definitions.map((def) => `${def.name}: ${def.type} at ${def.range.start.line}`).join("\n")
}
```

## 总结

跨文件代码补全功能通过以下方式提升开发体验：

1. **自动化**: 无需手动配置，自动分析依赖关系
2. **智能化**: 结合LSP和备用搜索，提供准确信息
3. **高效化**: 多层缓存机制，确保快速响应
4. **可扩展**: 支持多种语言和自定义配置

开始使用这个功能，让AI更好地理解您的代码结构，提供更准确的代码补全和分析！

## 下一步

- 查看 [README.md](./README.md) 了解详细功能介绍
- 查看 [ARCHITECTURE.md](./ARCHITECTURE.md) 了解系统架构
- 查看 [API_REFERENCE.md](./API_REFERENCE.md) 了解API详情
- 查看测试文件了解具体用法示例
