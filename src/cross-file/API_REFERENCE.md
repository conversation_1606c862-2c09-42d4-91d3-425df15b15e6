# 跨文件代码补全功能 API 参考

## 核心接口

### ImportDefinitionsService

跨文件定义服务的核心类，负责分析文件依赖和生成跨文件感知内容。

#### 构造函数

```typescript
constructor()
```

创建一个新的 ImportDefinitionsService 实例。

#### 方法

##### get(uri: vscode.Uri): Promise<CrossFileInfo | undefined>

获取指定文件的跨文件信息。

**参数**:

- `uri`: VS Code URI 对象，指向要分析的文件

**返回值**:

- `Promise<CrossFileInfo | undefined>`: 跨文件信息对象，如果分析失败则返回 undefined

**示例**:

```typescript
const service = new ImportDefinitionsService()
const uri = vscode.Uri.file("/path/to/file.java")
const crossFileInfo = await service.get(uri)
```

##### generateCrossFileDefinitions(filePath: string): Promise<string>

生成指定文件的跨文件感知定义内容。

**参数**:

- `filePath`: 文件的绝对路径

**返回值**:

- `Promise<string>`: 格式化的跨文件定义内容

**示例**:

```typescript
const definitions = await service.generateCrossFileDefinitions("/path/to/Controller.java")
console.log(definitions)
// 输出:
// [list_cross_code_definition_names for 'Model.java'] Result:
// # Model.java
// 1--10 | package com.example.model;
// 11--20 | public class Model { ... }
```

##### generateDefinitionsWithMembers(filePath: string): Promise<string | null>

生成包含类成员属性的定义内容。

**参数**:

- `filePath`: 文件的绝对路径

**返回值**:

- `Promise<string | null>`: 包含成员属性的定义内容，失败时返回 null

### RootPathContextService

基于AST路径的上下文服务类。

#### 构造函数

```typescript
constructor(importDefinitionsService: ImportDefinitionsService)
```

**参数**:

- `importDefinitionsService`: ImportDefinitionsService 实例

#### 方法

##### get(uri: vscode.Uri, cursorIndex: number): Promise<CrossFile[]>

获取指定位置的上下文信息。

**参数**:

- `uri`: 文件的 VS Code URI
- `cursorIndex`: 光标在文件中的索引位置

**返回值**:

- `Promise<CrossFile[]>`: 相关的跨文件信息数组

##### getContextForPath(uri: vscode.Uri, astPath: AstPath): Promise<CrossFile[]>

根据AST路径获取上下文信息。

**参数**:

- `uri`: 文件的 VS Code URI
- `astPath`: AST节点路径数组

**返回值**:

- `Promise<CrossFile[]>`: 相关的跨文件信息数组

### CrossFileDefinitionsService

独立的跨文件定义工具服务。

#### 构造函数

```typescript
constructor()
```

#### 方法

##### generateCrossFileDefinitions(filePath: string): Promise<string>

生成跨文件感知内容，类似 `list_code_definition_names` 但包含类成员属性。

**参数**:

- `filePath`: 待分析文件的路径

**返回值**:

- `Promise<string>`: 跨文件感知内容字符串

## 数据类型

### CrossFile

跨文件传输数据接口。

```typescript
interface CrossFile {
	kind: string // 文件类型
	path: string // 文件路径
	content: string // 函数代码
	depth: number | null // 调用层级
}
```

**字段说明**:

- `kind`: 文件类型，可选值：
    - `"DependentFile"`: 当前代码引用的文件
    - `"NeighbourFile"`: 基于文本相似性的相似文件
    - `"RetrievalSnippet"`: 从知识库检索的相关代码片段
- `path`: 文件的相对路径
- `content`: 相关的代码内容
- `depth`: 调用层级，通常为1

### RangeInFile

文件中的范围信息。

```typescript
interface RangeInFile {
	filepath: string
	range: Range
	uri?: vscode.Uri
}
```

### SymbolWithRange

带有范围信息的符号。

```typescript
interface SymbolWithRange extends RangeInFile {
	name: string
	type: Node["type"]
	content: string
}
```

### CrossFileInfo

跨文件信息对象。

```typescript
interface CrossFileInfo {
	imports: { [key: string]: RangeInFileWithContents[] }
}
```

### AutocompleteSnippetDeprecated

自动补全代码片段（已弃用）。

```typescript
interface AutocompleteSnippetDeprecated {
	filepath: string
	range: Range
	contents: string
}
```

## 工具函数

### AST 相关

#### getAst(filepath: string, fileContents: string): Promise<Tree | undefined>

获取文件的抽象语法树。

**参数**:

- `filepath`: 文件路径
- `fileContents`: 文件内容

**返回值**:

- `Promise<Tree | undefined>`: Tree-sitter AST对象

#### getTreePathAtCursor(ast: Tree, cursorIndex: number): Promise<AstPath>

获取光标位置的AST路径。

**参数**:

- `ast`: Tree-sitter AST对象
- `cursorIndex`: 光标索引位置

**返回值**:

- `Promise<AstPath>`: AST节点路径数组

### Tree-sitter 相关

#### getParserForFile(filepath: string): Promise<Parser | undefined>

获取文件对应的语言解析器。

**参数**:

- `filepath`: 文件路径

**返回值**:

- `Promise<Parser | undefined>`: Tree-sitter解析器实例

#### getLanguageForFile(filepath: string): Promise<Language | undefined>

获取文件对应的语言对象。

**参数**:

- `filepath`: 文件路径

**返回值**:

- `Promise<Language | undefined>`: Tree-sitter语言对象

#### getQueryForFile(filepath: string, type?: string): Promise<Query | undefined>

获取文件对应的查询对象。

**参数**:

- `filepath`: 文件路径
- `type`: 查询类型，默认为 "import-query"

**返回值**:

- `Promise<Query | undefined>`: Tree-sitter查询对象

#### getSymbolsForFile(uri: vscode.Uri, contents: string): Promise<SymbolWithRange[] | undefined>

提取文件中的符号信息。

**参数**:

- `uri`: 文件的VS Code URI
- `contents`: 文件内容

**返回值**:

- `Promise<SymbolWithRange[] | undefined>`: 符号信息数组

### IDE 集成

#### readFile(uri: vscode.Uri): Promise<string>

读取文件内容。

**参数**:

- `uri`: 文件的VS Code URI

**返回值**:

- `Promise<string>`: 文件内容字符串

#### gotoDefinition(uri: vscode.Uri, position: Position): Promise<RangeInFile[]>

跳转到定义。

**参数**:

- `uri`: 文件的VS Code URI
- `position`: 光标位置

**返回值**:

- `Promise<RangeInFile[]>`: 定义位置数组

#### readRangeInFile(fileUri: string, range: Range): Promise<string>

读取文件中指定范围的内容。

**参数**:

- `fileUri`: 文件URI或路径
- `range`: 要读取的范围

**返回值**:

- `Promise<string>`: 指定范围的内容

#### changeToWorkspacePath(path: string): string

将绝对路径转换为相对于工作空间的路径。

**参数**:

- `path`: 绝对文件路径

**返回值**:

- `string`: 相对路径

### LSP 集成

#### executeGotoProvider(input: GotoInput): Promise<RangeInFile[]>

执行LSP的跳转提供者。

**参数**:

- `input`: 跳转输入参数

```typescript
interface GotoInput {
	uri: vscode.Uri
	line: number
	character: number
	name: GotoProviderName
}

type GotoProviderName =
	| "vscode.executeDefinitionProvider"
	| "vscode.executeTypeDefinitionProvider"
	| "vscode.executeDeclarationProvider"
	| "vscode.executeImplementationProvider"
	| "vscode.executeReferenceProvider"
```

**返回值**:

- `Promise<RangeInFile[]>`: 查找结果数组

## 语言特定API

### JavaScript/TypeScript

#### queryJavascriptDefinition(uri: vscode.Uri): Promise<string>

获取JavaScript/TypeScript文件的第一层级函数、变量数据。

#### queryJavascriptDependentFiles(uri: vscode.Uri): Promise<any>

获取JavaScript/TypeScript文件的跨文件信息。

### Java

#### queryJavaClassDefinition(uri: vscode.Uri): Promise<string>

获取Java文件的跨文件类数据。

## 配置常量

### 支持的语言

```typescript
enum LanguageName {
	TYPESCRIPT = "typescript",
	JAVASCRIPT = "javascript",
	JAVA = "java",
	PYTHON = "python",
	// ... 其他语言
}
```

### 忽略路径模式

```typescript
const IGNORE_PATH_PATTERNS: Partial<Record<LanguageName, RegExp[]>> = {
	[LanguageName.JAVA]: [
		/.*\/target\/classes\/.*/,
		/.*\/build\/classes\/.*/,
		/.*\.jar!\/.*/,
		// ... 更多模式
	],
	// ... 其他语言的模式
}
```

### 缓存配置

```typescript
const MAX_CACHE_SIZE = 500 // LSP缓存最大大小
const CROSS_FILE_CACHE_SIZE = 20 // 跨文件缓存大小
const CACHE_TTL = 1000 * 60 * 10 // 缓存过期时间（10分钟）
```

## 错误处理

所有异步方法都可能抛出以下类型的错误：

- `FileNotFoundError`: 文件不存在
- `ParseError`: 语法解析失败
- `LSPError`: LSP服务调用失败
- `CacheError`: 缓存操作失败

建议使用 try-catch 块处理这些错误：

```typescript
try {
	const result = await service.generateCrossFileDefinitions(filePath)
	console.log(result)
} catch (error) {
	console.error("Failed to generate cross file definitions:", error)
	// 处理错误...
}
```

## 使用示例

### 基本用法

```typescript
import { ImportDefinitionsService } from "./ImportDefinitionsService"

// 创建服务实例
const service = new ImportDefinitionsService()

// 分析文件依赖
const uri = vscode.Uri.file("/path/to/Controller.java")
const crossFileInfo = await service.get(uri)

// 生成跨文件定义
const definitions = await service.generateCrossFileDefinitions("/path/to/Controller.java")
console.log(definitions)
```

### 集成到工具中

```typescript
// 在工具调用中使用
export async function listCrossCodeDefinitionNames(path: string): Promise<string> {
	const service = new CrossFileDefinitionsService()
	return await service.generateCrossFileDefinitions(path)
}
```

### 上下文分析

```typescript
import { RootPathContextService, ImportDefinitionsService } from "./cross-file"

const importService = new ImportDefinitionsService()
const contextService = new RootPathContextService(importService)

// 获取光标位置的上下文
const context = await contextService.get(uri, cursorIndex)
console.log("Related files:", context)
```
