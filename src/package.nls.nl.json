{"extension.displayName": "<PERSON><PERSON><PERSON>", "extension.description": "<PERSON><PERSON> compleet ontwi<PERSON>kel<PERSON><PERSON> van AI-agents in je editor.", "views.contextMenu.label": "<PERSON><PERSON><PERSON>", "views.terminalMenu.label": "<PERSON><PERSON><PERSON>", "views.activitybar.title": "<PERSON><PERSON><PERSON>", "views.sidebar.name": "<PERSON><PERSON><PERSON>", "command.newTask.title": "<PERSON><PERSON><PERSON>", "command.mcpServers.title": "MCP Servers", "command.prompts.title": "<PERSON><PERSON>", "command.history.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "command.marketplace.title": "Marktplaats", "command.openInEditor.title": "<PERSON>en in Editor", "command.settings.title": "Instellingen", "command.documentation.title": "Documentatie", "command.logout.title": "Afmelden", "command.openInNewTab.title": "Openen in Nieuw Tabblad", "command.explainCode.title": "Leg Code Uit", "command.fixCode.title": "Repareer Code", "command.improveCode.title": "Verbeter Code", "command.unitTest.title": "Eenheidstest", "command.codeReview.title": "Code beoordeling", "command.commentCode.title": "Code Commentaar", "command.addToContext.title": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "command.focusInput.title": "Focus op Invoerveld", "command.setCustomStoragePath.title": "Aangepast Opslagpad Instellen", "command.terminal.addToContext.title": "<PERSON><PERSON><PERSON><PERSON> aan <PERSON>n", "command.terminal.fixCommand.title": "Repareer Dit Commando", "command.terminal.explainCommand.title": "Leg Dit Commando Uit", "command.acceptInput.title": "Invoer/Suggestie Accepteren", "configuration.title": "<PERSON><PERSON><PERSON>", "commands.allowedCommands.description": "Commando's die automatisch kunnen worden uitgevoerd wanneer 'Altijd goedkeuren uitvoerbewerkingen' is ingeschakeld", "settings.vsCodeLmModelSelector.description": "Instellingen voor VSCode Language Model API", "settings.vsCodeLmModelSelector.vendor.description": "De leverancier van het taalmodel (bijv. copilot)", "settings.vsCodeLmModelSelector.family.description": "De familie van het taalmodel (bijv. gpt-4)", "settings.customStoragePath.description": "Aangepast opslagpad. Laat leeg om de standaardlocatie te gebruiken. Ondersteunt absolute paden (bijv. 'D:\\RooCodeStorage')", "settings.completion.enterprise_code_completion.description": "Bedrijfscode aanvullen", "settings.rooCodeCloudEnabled.description": "Ecloud Cloud inschakelen.", "settings.completion.debounce_time.description": "Code completie trigger vertraging in milliseconden (ms)", "settings.completion.completion_number.description": "Aantal kandidaten voor code-aanvulling", "settings.completion.inlineCompletion_granularity.description": "Voorkeuren voor aanvullende korrelgrootte", "settings.completion.inlineCompletion_granularity.singleRow": "<PERSON><PERSON>", "settings.completion.inlineCompletion_granularity.oneTimeMaximization": "Eenmalige maximalisatie", "settings.completion.inlineCompletion_granularity.balanced": "evenwicht", "settings.completion.multiple_line_Completion.description": "Meerdere regels code aanvulling", "settings.completion.multiple_line_Completion.autoCompletion": "Automatisch invullen", "settings.completion.multiple_line_Completion.triggerCompletion": "<PERSON>gger a<PERSON>lling", "settings.completion.multiple_line_Completion.autoCompletion.description": "Enter en Ctrl+K (cmd+k voor Apple) zorgen voor meerdere regelen", "settings.completion.multiple_line_Completion.triggerCompletion.description": "Alleen Ctrl+K (cmd+k op Apple-systemen) activeert meerdere regels, enter niet", "settings.completion.max_tokens_completion.description": "max_tokens voor code-aanvulling", "settings.dmt.maxTokens.description": "max_tokens voor multimodale vraag-en-antwoordcodes", "settings.serverBaseUrl.description": "URL base voor de Zhanlu server, standaard is https://api-wuxi-1.cmecloud.cn:8443"}