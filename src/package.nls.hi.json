{"extension.displayName": "<PERSON><PERSON><PERSON>:AI Coding Assistant-Intelligent partner in development", "extension.description": "<PERSON><PERSON><PERSON>:AI Coding Assistant-your intelligent partner in software development with automatic code generation", "command.newTask.title": "नया कार्य", "command.explainCode.title": "कोड समझाएं", "command.fixCode.title": "कोड ठीक करें", "command.improveCode.title": "कोड सुधारें", "command.unitTest.title": "यूनिट टेस्ट", "command.codeReview.title": "कोड समीक्षा", "command.commentCode.title": "कोड टिप्पणी जोड़ें", "command.addToContext.title": "संदर्भ में जोड़ें", "command.openInNewTab.title": "नए टैब में खोलें", "command.focusInput.title": "इनपुट फ़ील्ड पर फोकस करें", "command.setCustomStoragePath.title": "कस्टम स्टोरेज पाथ सेट करें", "command.terminal.addToContext.title": "टर्मिनल सामग्री को संदर्भ में जोड़ें", "command.terminal.fixCommand.title": "यह कमांड ठीक करें", "command.terminal.explainCommand.title": "यह कमांड समझाएं", "command.acceptInput.title": "इनपुट/सुझाव स्वीकारें", "views.activitybar.title": "<PERSON><PERSON><PERSON>", "views.contextMenu.label": "<PERSON><PERSON><PERSON>", "views.terminalMenu.label": "<PERSON><PERSON><PERSON>", "views.sidebar.name": "<PERSON><PERSON><PERSON>", "command.mcpServers.title": "एमसीपी सर्वर", "command.prompts.title": "मोड्स", "command.history.title": "इतिहास", "command.marketplace.title": "मार्केटप्लेस", "command.openInEditor.title": "एडिटर में खोलें", "command.settings.title": "सेटिंग्स", "command.documentation.title": "दस्तावेज़ीकरण", "command.logout.title": "साइन आउट", "configuration.title": "<PERSON><PERSON><PERSON>", "commands.allowedCommands.description": "वे कमांड जो स्वचालित रूप से निष्पादित की जा सकती हैं जब 'हमेशा निष्पादन संचालन को स्वीकृत करें' सक्रिय हो", "settings.vsCodeLmModelSelector.description": "VSCode भाषा मॉडल API के लिए सेटिंग्स", "settings.vsCodeLmModelSelector.vendor.description": "भाषा मॉडल का विक्रेता (उदा. copilot)", "settings.vsCodeLmModelSelector.family.description": "भाषा मॉडल का परिवार (उदा. gpt-4)", "settings.customStoragePath.description": "कस्टम स्टोरेज पाथ। डिफ़ॉल्ट स्थान का उपयोग करने के लिए खाली छोड़ें। पूर्ण पथ का समर्थन करता है (उदा. 'D:\\ZhanluStorage')", "settings.completion.enterprise_code_completion.description": "उद्योग कोड पूर्ण करें", "settings.rooCodeCloudEnabled.description": "Ecloud Cloud सक्षम करें।", "settings.completion.debounce_time.description": "कोड पूर्णता ट्रिगर विलंब मिलीसेकंड (एमएस)", "settings.completion.completion_number.description": "कोड पूर्णता उम्मीदवारों की संख्या", "settings.completion.inlineCompletion_granularity.description": "पूर्णता ग्रैन्युलैरिटी प्राथमिकता", "settings.completion.inlineCompletion_granularity.singleRow": "एकल पंक्ति", "settings.completion.inlineCompletion_granularity.oneTimeMaximization": "एक बार में अधिकतम", "settings.completion.inlineCompletion_granularity.balanced": "संतुलित", "settings.completion.multiple_line_Completion.description": "बहु-पंक्ति कोड पूर्णता विधि", "settings.completion.multiple_line_Completion.autoCompletion": "स्वचालित पूर्णता", "settings.completion.multiple_line_Completion.triggerCompletion": "ट्रिगर पूर्णता", "settings.completion.multiple_line_Completion.autoCompletion.description": "एंटर और Ctrl+K (मैक पर cmd+K) दोनों बहु-पंक्ति पूर्णता को ट्रिगर कर सकते हैं", "settings.completion.multiple_line_Completion.triggerCompletion.description": "केवल Ctrl+K (मैक पर cmd+K) बहु-पंक्ति पूर्णता को ट्रिगर करता है, एंटर नहीं", "settings.completion.max_tokens_completion.description": "कोड पूर्णता के लिए अधिकतम टोकन", "settings.dmt.maxTokens.description": "टोकन कोड मल्टीमोडल प्रश्न और उत्तर के लिए max_tokens", "settings.serverBaseUrl.description": "<PERSON><PERSON><PERSON> सर्वर के लिए बेस URL, डिफ़ॉल्ट रूप से https://api-wuxi-1.cmecloud.cn:8443 है"}