{"extension.displayName": "<PERSON><PERSON><PERSON>", "extension.description": "在你的编辑器中提供完整的 AI 代理开发团队。", "command.newTask.title": "新建任务", "command.explainCode.title": "解释代码", "command.fixCode.title": "修复代码", "command.improveCode.title": "改进代码", "command.addToContext.title": "添加到上下文", "command.openInNewTab.title": "在新标签页中打开", "command.focusInput.title": "聚焦输入框", "command.setCustomStoragePath.title": "设置自定义存储路径", "command.terminal.addToContext.title": "将终端内容添加到上下文", "command.terminal.fixCommand.title": "修复此命令", "command.terminal.explainCommand.title": "解释此命令", "command.acceptInput.title": "接受输入/建议", "views.activitybar.title": "<PERSON><PERSON><PERSON>", "views.contextMenu.label": "<PERSON><PERSON><PERSON>", "views.terminalMenu.label": "<PERSON><PERSON><PERSON>", "command.mcpServers.title": "MCP 服务器", "command.prompts.title": "提示", "command.history.title": "历史记录", "command.marketplace.title": "应用市场", "command.openInEditor.title": "在编辑器中打开", "command.settings.title": "设置", "command.documentation.title": "文档", "configuration.title": "<PERSON><PERSON><PERSON>", "commands.allowedCommands.description": "当启用'始终批准执行操作'时可以自动执行的命令", "command.roomoteAgent.title": "远程代理", "settings.vsCodeLmModelSelector.description": "VSCode 语言模型 API 的设置", "settings.vsCodeLmModelSelector.vendor.description": "语言模型的供应商（例如：copilot）", "settings.vsCodeLmModelSelector.family.description": "语言模型的系列（例如：gpt-4）", "settings.completion.enterprise_code_completion.description": "企业代码补全", "settings.customStoragePath.description": "自定义存储路径。留空以使用默认位置。支持绝对路径（例如：'D:\\ZhanluStorage'）", "settings.completion.debounce_time.description": "代码补全触发时延毫秒(ms)", "settings.completion.completion_number.description": "代码补全生成候选个数", "settings.completion.inlineCompletion_granularity.description": "补全粒度的偏好", "settings.completion.inlineCompletion_granularity.singleRow": "单行", "settings.completion.inlineCompletion_granularity.oneTimeMaximization": "一次性最大化", "settings.completion.inlineCompletion_granularity.balanced": "均衡", "settings.completion.multiple_line_Completion.description": "多行代码补全方式", "settings.completion.multiple_line_Completion.autoCompletion": "自动补全", "settings.completion.multiple_line_Completion.triggerCompletion": "触发补全", "settings.completion.multiple_line_Completion.autoCompletion.description": "enter及Ctrl+K(苹果系统为cmd+k)均可触发多行补全", "settings.completion.multiple_line_Completion.triggerCompletion.description": "仅Ctrl+K(苹果系统为cmd+k)触发多行补全，enter不触发", "settings.completion.max_tokens_completion.description": "代码补全的max_tokens", "settings.dmt.maxTokens.description": "图生代码多模态问答的max_tokens", "settings.serverBaseUrl.description": "湛卢服务端的基础URL地址，默认为 https://api-wuxi-1.cmecloud.cn:8443"}