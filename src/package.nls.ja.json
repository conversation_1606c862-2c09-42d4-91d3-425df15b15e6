{"extension.displayName": "<PERSON><PERSON><PERSON>", "extension.description": "エディタ内のAIエージェントによる開発チーム。", "views.contextMenu.label": "<PERSON><PERSON><PERSON>", "views.terminalMenu.label": "<PERSON><PERSON><PERSON>", "views.activitybar.title": "<PERSON><PERSON><PERSON>", "views.sidebar.name": "<PERSON><PERSON><PERSON>", "command.newTask.title": "新しいタスク", "command.mcpServers.title": "MCPサーバー", "command.prompts.title": "モード", "command.history.title": "履歴", "command.marketplace.title": "マーケットプレイス", "command.openInEditor.title": "エディタで開く", "command.settings.title": "設定", "command.documentation.title": "ドキュメント", "command.openInNewTab.title": "新しいタブで開く", "command.explainCode.title": "コードの説明", "command.fixCode.title": "コードの修正", "command.improveCode.title": "コードの改善", "command.unitTest.title": "単体テスト", "command.codeReview.title": "コードレビュー", "command.commentCode.title": "コードのコメント", "command.addToContext.title": "コンテキストに追加", "command.focusInput.title": "入力フィールドにフォーカス", "command.setCustomStoragePath.title": "カスタムストレージパスの設定", "command.terminal.addToContext.title": "ターミナルの内容をコンテキストに追加", "command.terminal.fixCommand.title": "このコマンドを修正", "command.terminal.explainCommand.title": "このコマンドを説明", "command.acceptInput.title": "入力/提案を承認", "configuration.title": "<PERSON><PERSON><PERSON>", "commands.allowedCommands.description": "'常に実行操作を承認する'が有効な場合に自動実行できるコマンド", "settings.vsCodeLmModelSelector.description": "VSCode 言語モデル API の設定", "settings.vsCodeLmModelSelector.vendor.description": "言語モデルのベンダー（例：copilot）", "settings.vsCodeLmModelSelector.family.description": "言語モデルのファミリー（例：gpt-4）", "settings.customStoragePath.description": "カスタムストレージパス。デフォルトの場所を使用する場合は空のままにします。絶対パスをサポートします（例：'D:\\ZhanluStorage'）", "settings.rooCodeCloudEnabled.description": "Ecloud Cloud を有効にする。", "settings.completion.debounce_time.description": "コード補完のトリガー遅延ミリ秒(ms)", "settings.completion.completion_number.description": "コード補完の候補生成数", "settings.completion.inlineCompletion_granularity.description": "補完粒度の設定", "settings.completion.inlineCompletion_granularity.singleRow": "単一行", "settings.completion.inlineCompletion_granularity.oneTimeMaximization": "一度に最大化", "settings.completion.inlineCompletion_granularity.balanced": "バランス", "settings.completion.multiple_line_Completion.description": "複数行コード補完方式", "settings.completion.multiple_line_Completion.autoCompletion": "自動補完", "settings.completion.multiple_line_Completion.triggerCompletion": "トリガー補完", "settings.completion.multiple_line_Completion.autoCompletion.description": "EnterおよびCtrl+K(Appleシステムではcmd+K)で複数行補完をトリガー", "settings.completion.multiple_line_Completion.triggerCompletion.description": "Ctrl+K(Appleシステムではcmd+K)のみで複数行補完をトリガー、Enterではトリガーしない", "settings.completion.max_tokens_completion.description": "コード補完のmax_tokens", "settings.dmt.maxTokens.description": "図形生成コードマルチモーダル質疑応答のmax _ tokens", "settings.serverBaseUrl.description": "Zhanluサーバーのベースurl、デフォルトは https://api-wuxi-1.cmecloud.cn:8443"}