{"extension.displayName": "<PERSON><PERSON><PERSON>", "extension.description": "Целая команда ИИ-разработчиков в вашем редакторе.", "views.contextMenu.label": "<PERSON><PERSON><PERSON>", "views.terminalMenu.label": "<PERSON><PERSON><PERSON>", "views.activitybar.title": "<PERSON><PERSON><PERSON>", "command.newTask.title": "Новая задача", "command.mcpServers.title": "MCP серверы", "command.prompts.title": "Промпты", "command.history.title": "История", "command.marketplace.title": "Маркетплейс", "command.openInEditor.title": "Открыть в редакторе", "command.settings.title": "Настройки", "command.documentation.title": "Документация", "command.openInNewTab.title": "Открыть в новой вкладке", "command.explainCode.title": "Объяснить код", "command.fixCode.title": "Исправить код", "command.improveCode.title": "Улучшить код", "command.unitTest.title": "Модульный тест", "command.codeReview.title": "Крит<PERSON><PERSON><PERSON> кода", "command.commentCode.title": "Комментировать код", "command.addToContext.title": "Добавить в контекст", "command.focusInput.title": "Фокус на поле ввода", "command.setCustomStoragePath.title": "Указать путь хранения", "command.terminal.addToContext.title": "Добавить содержимое терминала в контекст", "command.terminal.fixCommand.title": "Исправить эту команду", "command.terminal.explainCommand.title": "Объяснить эту команду", "command.acceptInput.title": "Принять ввод/предложение", "configuration.title": "<PERSON><PERSON><PERSON>", "commands.allowedCommands.description": "Команды, которые могут быть автоматически выполнены, когда включена опция 'Всегда подтверждать операции выполнения'", "settings.vsCodeLmModelSelector.description": "Настройки для VSCode Language Model API", "settings.vsCodeLmModelSelector.vendor.description": "Поставщик языковой модели (например, copilot)", "settings.vsCodeLmModelSelector.family.description": "Семейство языковой модели (например, gpt-4)", "settings.completion.debounce_time.description": "Задержка запуска с заполнением кода в миллисекундах (ms)", "settings.completion.completion_number.description": "Заполнение кода Создать число кандидатов", "settings.completion.inlineCompletion_granularity.description": "Предпочтение полноразмерности.", "settings.completion.inlineCompletion_granularity.singleRow": "Однострочный", "settings.completion.inlineCompletion_granularity.oneTimeMaximization": "Одноразовая максимизация", "settings.completion.inlineCompletion_granularity.balanced": "Равновесие", "settings.completion.multiple_line_Completion.description": "Многострочный способ дополнения кода", "settings.completion.multiple_line_Completion.autoCompletion": "Автозаполнение", "settings.completion.multiple_line_Completion.triggerCompletion": "Триггерное дополнение", "settings.completion.multiple_line_Completion.autoCompletion.description": "enter и Ctrl + K (для системы Apple cmd + k) могут запускать многорядные дополнения", "settings.completion.multiple_line_Completion.triggerCompletion.description": "Только Ctrl + K (система Apple для cmd + k) запускает многорядное дополнение, enter не запускает", "settings.completion.max_tokens_completion.description": "Полный код max tokens", "settings.dmt.maxTokens.description": "многомодальные вопросы и ответы max tokens", "settings.serverBaseUrl.description": "Базовый URL для сервер<PERSON>, по умолчанию https://api-wuxi-1.cmecloud.cn:8443"}