{"extension.displayName": "<PERSON><PERSON><PERSON>:AI Coding Assistant-Intelligent partner in development", "extension.description": "<PERSON><PERSON><PERSON>:AI Coding Assistant-your intelligent partner in software development with automatic code generation", "command.newTask.title": "Neue Aufgabe", "command.explainCode.title": "Code Erklären", "command.fixCode.title": "Code Reparieren", "command.improveCode.title": "Code Verbessern", "command.unitTest.title": "Unit-Test", "command.codeReview.title": "Code-Bewertung", "command.commentCode.title": "Code Kommentieren", "command.addToContext.title": "Zum Kontext Hinzufügen", "command.openInNewTab.title": "In Neuem <PERSON>", "command.focusInput.title": "Eingabefeld Fokussieren", "command.setCustomStoragePath.title": "Benutzerdefinierten Speicherpfad Festlegen", "command.terminal.addToContext.title": "Terminal-Inhalt zum Kontext Hinzufügen", "command.terminal.fixCommand.title": "<PERSON><PERSON> Befehl Reparieren", "command.terminal.explainCommand.title": "Diesen Befehl Erklären", "command.acceptInput.title": "Eingabe/Vorschlag Akzeptieren", "views.activitybar.title": "<PERSON><PERSON><PERSON>", "views.contextMenu.label": "<PERSON><PERSON><PERSON>", "views.terminalMenu.label": "<PERSON><PERSON><PERSON>", "views.sidebar.name": "<PERSON><PERSON><PERSON>", "command.mcpServers.title": "MCP Server", "command.prompts.title": "<PERSON><PERSON>", "command.history.title": "<PERSON><PERSON><PERSON><PERSON>", "command.marketplace.title": "Marktplatz", "command.openInEditor.title": "Im Editor <PERSON>", "command.settings.title": "Einstellungen", "command.documentation.title": "Dokumentation", "command.logout.title": "Abmelden", "configuration.title": "<PERSON><PERSON><PERSON>", "commands.allowedCommands.description": "<PERSON><PERSON><PERSON><PERSON>, die automatisch ausgeführt werden können, wenn 'Ausführungsoperationen immer genehmigen' aktiviert ist", "settings.vsCodeLmModelSelector.description": "Einstellungen für die VSCode-Sprachmodell-API", "settings.vsCodeLmModelSelector.vendor.description": "Der Anbieter des Sprachmodells (z.B. copilot)", "settings.vsCodeLmModelSelector.family.description": "Die Familie des Sprachmodells (z.B. gpt-4)", "settings.customStoragePath.description": "Benutzerdefinierter Speicherpfad. <PERSON><PERSON>, um den Standardspeicherort zu verwenden. Unterstützt absolute Pfade (z.B. 'D:\\ZhanluStorage')", "settings.completion.enterprise_code_completion.description": "Unternehmenscode ergänzen", "settings.rooCodeCloudEnabled.description": "Aktiviere Ecloud Cloud.", "settings.completion.debounce_time.description": "Verzögerungszeit in Millisekunden (ms) für Code-Vervollständigung", "settings.completion.completion_number.description": "Anzahl der generierten Vervollständigungsvorschläge", "settings.completion.inlineCompletion_granularity.description": "Präferenz für Granularität der Vervollständigung", "settings.completion.inlineCompletion_granularity.singleRow": "Einzelzeile", "settings.completion.inlineCompletion_granularity.oneTimeMaximization": "Einmalige Maximierung", "settings.completion.inlineCompletion_granularity.balanced": "Ausgewogen", "settings.completion.multiple_line_Completion.description": "Mehrzeilige Code-Vervollständigungsmethode", "settings.completion.multiple_line_Completion.autoCompletion": "Automatische Vervollständigung", "settings.completion.multiple_line_Completion.triggerCompletion": "Ausgelöste Vervollständigung", "settings.completion.multiple_line_Completion.autoCompletion.description": "Enter und Ctrl+K (Cmd+K auf Mac) lösen mehrzeilige Vervollständigung aus", "settings.completion.multiple_line_Completion.triggerCompletion.description": "Nur Ctrl+K (Cmd+K auf Mac) löst mehrzeilige Vervollständigung aus, <PERSON><PERSON> nicht", "settings.completion.max_tokens_completion.description": "Maximale Token-Anzahl für Code-Vervollständigung", "settings.dmt.maxTokens.description": "max_tokens für multimodale Fragen und Antworten", "settings.serverBaseUrl.description": "Basis-URL für den Zhanlu-Server, standardmäßig ist https://api-wuxi-1.cmecloud.cn:8443"}