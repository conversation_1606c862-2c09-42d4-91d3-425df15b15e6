{"extension.displayName": "<PERSON><PERSON><PERSON>:AI Coding Assistant-Intelligent partner in development", "extension.description": "<PERSON><PERSON><PERSON>:AI Coding Assistant-your intelligent partner in software development with automatic code generation", "command.newTask.title": "Nueva Tarea", "command.explainCode.title": "Explicar Código", "command.fixCode.title": "<PERSON><PERSON><PERSON><PERSON>", "command.improveCode.title": "<PERSON><PERSON><PERSON>", "command.unitTest.title": "Prueba Unitaria", "command.codeReview.title": "Revisión del Código", "command.commentCode.title": "Comentar Código", "command.addToContext.title": "<PERSON><PERSON><PERSON> Contexto", "command.openInNewTab.title": "Abrir en Nueva Pestaña", "command.focusInput.title": "Enfocar Campo de Entrada", "command.setCustomStoragePath.title": "Establecer <PERSON> de Almacenamiento Personalizada", "command.terminal.addToContext.title": "Añadir Contenido de Terminal al Contexto", "command.terminal.fixCommand.title": "<PERSON><PERSON><PERSON><PERSON>", "command.terminal.explainCommand.title": "Explicar Este Comando", "command.acceptInput.title": "Aceptar Entrada/Sugerencia", "views.activitybar.title": "<PERSON><PERSON><PERSON>", "views.contextMenu.label": "<PERSON><PERSON><PERSON>", "views.terminalMenu.label": "<PERSON><PERSON><PERSON>", "views.sidebar.name": "<PERSON><PERSON><PERSON>", "command.mcpServers.title": "Servidores MCP", "command.prompts.title": "Modos", "command.history.title": "Historial", "command.marketplace.title": "<PERSON><PERSON><PERSON>", "command.openInEditor.title": "Abrir en Editor", "command.settings.title": "Configuración", "command.documentation.title": "Documentación", "command.logout.title": "<PERSON><PERSON><PERSON>", "configuration.title": "<PERSON><PERSON><PERSON>", "commands.allowedCommands.description": "Comandos que pueden ejecutarse automáticamente cuando 'Aprobar siempre operaciones de ejecución' está activado", "settings.vsCodeLmModelSelector.description": "Configuración para la API del modelo de lenguaje VSCode", "settings.vsCodeLmModelSelector.vendor.description": "El proveedor del modelo de lenguaje (ej. copilot)", "settings.vsCodeLmModelSelector.family.description": "La familia del modelo de lenguaje (ej. gpt-4)", "settings.customStoragePath.description": "Ruta de almacenamiento personalizada. Dejar vacío para usar la ubicación predeterminada. Admite rutas absolutas (ej. 'D:\\ZhanluStorage')", "settings.completion.enterprise_code_completion.description": "Complemento de código empresarial", "settings.rooCodeCloudEnabled.description": "Habilitar Ecloud Cloud.", "settings.completion.debounce_time.description": "Retraso en milisegundos (ms) para activar la autocompletación de código", "settings.completion.completion_number.description": "Número de candidatos generados para autocompletación", "settings.completion.inlineCompletion_granularity.description": "Preferencia de granularidad de autocompletación", "settings.completion.inlineCompletion_granularity.singleRow": "Línea <PERSON>", "settings.completion.inlineCompletion_granularity.oneTimeMaximization": "Maximización de una vez", "settings.completion.inlineCompletion_granularity.balanced": "Equilibrado", "settings.completion.multiple_line_Completion.description": "Método de autocompletación de múltiples líneas", "settings.completion.multiple_line_Completion.autoCompletion": "Autocompletación automática", "settings.completion.multiple_line_Completion.triggerCompletion": "Autocompletación por activación", "settings.completion.multiple_line_Completion.autoCompletion.description": "Enter y Ctrl+K (Cmd+K en Mac) pueden activar autocompletación de múltiples líneas", "settings.completion.multiple_line_Completion.triggerCompletion.description": "Solo Ctrl+K (Cmd+K en Mac) activa autocompletación de múltiples líneas, Enter no lo hace", "settings.completion.max_tokens_completion.description": "max_tokens para autocompletación de código", "settings.dmt.maxTokens.description": "Max tokens para preguntas y respuestas multimodal de código de generación de imágenes", "settings.serverBaseUrl.description": "URL base para el servidor <PERSON>, por defecto es https://api-wuxi-1.cmecloud.cn:8443"}