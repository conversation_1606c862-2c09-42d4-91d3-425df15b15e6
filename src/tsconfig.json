{"compilerOptions": {"types": ["vitest/globals"], "esModuleInterop": true, "experimentalDecorators": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "lib": ["es2022", "esnext.disposable", "DOM"], "module": "esnext", "moduleResolution": "<PERSON><PERSON><PERSON>", "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "noImplicitReturns": true, "noUnusedLocals": false, "resolveJsonModule": true, "rootDir": ".", "skipLibCheck": true, "sourceMap": true, "strict": true, "target": "es2022", "useDefineForClassFields": true, "useUnknownInCatchVariables": false}, "include": ["."], "exclude": ["node_modules"]}