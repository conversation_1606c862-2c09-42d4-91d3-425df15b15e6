{"extension.displayName": "<PERSON><PERSON><PERSON>", "extension.description": "<PERSON><PERSON>t đội ngũ phát triển các tác nhân AI hoàn chỉnh trong trình soạn thảo của bạn.", "command.newTask.title": "<PERSON><PERSON><PERSON>", "command.explainCode.title": "<PERSON><PERSON><PERSON><PERSON>", "command.fixCode.title": "<PERSON><PERSON><PERSON>", "command.improveCode.title": "<PERSON><PERSON><PERSON>", "command.unitTest.title": "<PERSON><PERSON><PERSON>", "command.codeReview.title": "Đánh giá code", "command.commentCode.title": "<PERSON><PERSON><PERSON> luận mã", "command.addToContext.title": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON>", "command.openInNewTab.title": "Mở trong Tab Mới", "command.focusInput.title": "<PERSON>ậ<PERSON>rung vào <PERSON>", "command.setCustomStoragePath.title": "Đặt Đường Dẫn Lưu Trữ Tùy Chỉnh", "command.terminal.addToContext.title": "<PERSON><PERSON><PERSON>m <PERSON>i Dung Terminal vào Ngữ Cảnh", "command.terminal.fixCommand.title": "<PERSON><PERSON><PERSON>", "command.terminal.explainCommand.title": "<PERSON><PERSON><PERSON><PERSON>", "command.acceptInput.title": "<PERSON>ấ<PERSON>/<PERSON><PERSON><PERSON> Ý", "views.activitybar.title": "<PERSON><PERSON><PERSON>", "views.contextMenu.label": "<PERSON><PERSON><PERSON>", "views.terminalMenu.label": "<PERSON><PERSON><PERSON>", "views.sidebar.name": "<PERSON><PERSON><PERSON>", "command.mcpServers.title": "<PERSON><PERSON><PERSON> MCP", "command.prompts.title": "Chế Độ", "command.history.title": "<PERSON><PERSON><PERSON>", "command.marketplace.title": "Marketplace", "command.openInEditor.title": "Mở trong Trình <PERSON>", "command.settings.title": "Cài Đặt", "command.documentation.title": "<PERSON><PERSON><PERSON>", "configuration.title": "<PERSON><PERSON><PERSON>", "commands.allowedCommands.description": "<PERSON><PERSON><PERSON> l<PERSON>nh có thể được thực thi tự động khi 'Luôn phê duyệt các thao tác thực thi' đ<PERSON><PERSON><PERSON> bật", "settings.vsCodeLmModelSelector.description": "Cài đặt cho API mô hình ngôn ngữ VSCode", "settings.vsCodeLmModelSelector.vendor.description": "<PERSON><PERSON><PERSON> cung cấp mô hình ngôn ngữ (ví dụ: copilot)", "settings.vsCodeLmModelSelector.family.description": "<PERSON><PERSON> mô hình ngôn ngữ (ví dụ: gpt-4)", "settings.customStoragePath.description": "Đường dẫn lưu trữ tùy chỉnh. Để trống để sử dụng vị trí mặc định. Hỗ trợ đường dẫn tuyệt đối (ví dụ: 'D:\\ZhanluStorage')", "settings.rooCodeCloudEnabled.description": "Bật Ecloud Cloud.", "settings.completion.debounce_time.description": "<PERSON><PERSON><PERSON><PERSON> gian tr<PERSON> kích ho<PERSON>t bổ sung mã (ms)", "settings.completion.completion_number.description": "<PERSON><PERSON> lượng <PERSON>ng viên bổ sung mã đư<PERSON><PERSON> tạo", "settings.completion.inlineCompletion_granularity.description": "Sở thích về mức độ chi tiết của bổ sung", "settings.completion.inlineCompletion_granularity.singleRow": "Dòng đơn", "settings.completion.inlineCompletion_granularity.oneTimeMaximization": "<PERSON><PERSON><PERSON> đa hóa một lần", "settings.completion.inlineCompletion_granularity.balanced": "Cân bằng", "settings.completion.multiple_line_Completion.description": "<PERSON><PERSON><PERSON><PERSON> thứ<PERSON> b<PERSON> sung mã nhiều dòng", "settings.completion.multiple_line_Completion.autoCompletion": "<PERSON><PERSON> động bổ sung", "settings.completion.multiple_line_Completion.triggerCompletion": "<PERSON><PERSON><PERSON> b<PERSON> sung", "settings.completion.multiple_line_Completion.autoCompletion.description": "Enter và Ctrl+K (cmd+k trên Mac) đều có thể kích hoạt bổ sung nhiều dòng", "settings.completion.multiple_line_Completion.triggerCompletion.description": "Chỉ Ctrl+K (cmd+k trên Mac) kích hoạt bổ sung nhiều dòng, <PERSON>ter không kích hoạt", "settings.completion.max_tokens_completion.description": "max_tokens cho bổ sung mã", "settings.dmt.maxTokens.description": "Câu hỏi max_tokens", "settings.serverBaseUrl.description": "URL cơ sở cho má<PERSON> ch<PERSON>, mặc định là https://api-wuxi-1.cmecloud.cn:8443"}