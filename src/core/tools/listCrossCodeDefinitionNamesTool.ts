import path from "path"

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ush<PERSON>ool<PERSON><PERSON>ult, RemoveClosingTag } from "../../shared/tools"
import { Task } from "../task/Task"
import { ClineSayTool } from "../../shared/ExtensionMessage"
import { getReadablePath } from "../../utils/path"
import { isPathOutsideWorkspace } from "../../utils/pathUtils"
import { CrossFileDefinitionsService } from "../../services/CrossFileDefinitionsService"

export async function listCrossCodeDefinitionNamesTool(
	cline: Task,
	block: ToolUse,
	askApproval: AskApproval,
	handleError: HandleError,
	pushToolResult: PushToolResult,
	removeClosingTag: RemoveClosingTag,
) {
	const relPath: string | undefined = block.params.path

	// Calculate if the path is outside workspace
	const absolutePath = relPath ? path.resolve(cline.cwd, relPath) : cline.cwd
	const isOutsideWorkspace = isPathOutsideWorkspace(absolutePath)

	const sharedMessageProps: ClineSayTool = {
		tool: "listCrossCodeDefinitionNames",
		path: getReadablePath(cline.cwd, removeClosingTag("path", relPath)),
		isOutsideWorkspace,
	}

	try {
		if (block.partial) {
			const partialMessage = JSON.stringify({ ...sharedMessageProps, content: "" } satisfies ClineSayTool)
			await cline.ask("tool", partialMessage, block.partial).catch(() => {})
			return
		} else {
			if (!relPath) {
				cline.consecutiveMistakeCount++
				cline.recordToolError("list_cross_code_definition_names")
				pushToolResult(await cline.sayAndCreateMissingParamError("list_cross_code_definition_names", "path"))
				return
			}

			cline.consecutiveMistakeCount = 0

			let result: string

			try {
				// 使用CrossFileDefinitionsService生成跨文件感知内容
				const crossFileDefinitionsService = new CrossFileDefinitionsService()
				const crossFileDefinitions =
					await crossFileDefinitionsService.generateCrossFileDefinitions(absolutePath)

				if (crossFileDefinitions) {
					result = crossFileDefinitions
				} else {
					result = "No cross-file definitions found for the specified file."
				}
			} catch (error) {
				console.warn("Failed to generate cross file definitions:", error)
				result = `Failed to generate cross-file definitions: ${error instanceof Error ? error.message : String(error)}`
			}

			const completeMessage = JSON.stringify({ ...sharedMessageProps, content: result } satisfies ClineSayTool)
			const didApprove = await askApproval("tool", completeMessage)

			if (!didApprove) {
				return
			}

			pushToolResult(result)
			return
		}
	} catch (error) {
		await handleError("generating cross-file definitions", error)
		return
	}
}
