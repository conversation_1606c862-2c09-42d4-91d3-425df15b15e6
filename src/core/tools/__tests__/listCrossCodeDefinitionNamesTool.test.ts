import { describe, it, expect, vi, beforeEach, type Mock } from "vitest"
import { listCrossCodeDefinitionNamesTool } from "../listCrossCodeDefinitionNamesTool"
import { Task } from "../../task/Task"
import { ToolUse } from "../../../shared/tools"

// Mock dependencies
vi.mock("../../../services/CrossFileDefinitionsService", () => ({
	CrossFileDefinitionsService: vi.fn().mockImplementation(() => ({
		generateCrossFileDefinitions: vi.fn().mockResolvedValue("mock cross file definitions"),
	})),
}))

describe("listCrossCodeDefinitionNamesTool", () => {
	let mockTask: Partial<Task>
	let mockAskApproval: Mock
	let mockHandleError: Mock
	let mockPushToolResult: Mock
	let mockRemoveClosingTag: Mock

	beforeEach(() => {
		mockTask = {
			cwd: "/test/workspace",
			consecutiveMistakeCount: 0,
			recordToolError: vi.fn(),
			sayAndCreateMissingParamError: vi.fn().mockResolvedValue("Missing parameter error"),
			ask: vi.fn().mockResolvedValue(undefined),
		}

		mockAskApproval = vi.fn().mockResolvedValue(true)
		mockHandleError = vi.fn()
		mockPushToolResult = vi.fn()
		mockRemoveClosingTag = vi.fn().mockImplementation((tag, content) => content)
	})

	it("should handle missing path parameter", async () => {
		const block: ToolUse = {
			type: "tool_use",
			name: "list_cross_code_definition_names",
			params: {},
			partial: false,
		}

		await listCrossCodeDefinitionNamesTool(
			mockTask as Task,
			block,
			mockAskApproval,
			mockHandleError,
			mockPushToolResult,
			mockRemoveClosingTag,
		)

		expect(mockTask.consecutiveMistakeCount).toBe(1)
		expect(mockTask.recordToolError).toHaveBeenCalledWith("list_cross_code_definition_names")
		expect(mockTask.sayAndCreateMissingParamError).toHaveBeenCalledWith("list_cross_code_definition_names", "path")
	})

	it("should handle partial requests", async () => {
		const block: ToolUse = {
			type: "tool_use",
			name: "list_cross_code_definition_names",
			params: { path: "test.java" },
			partial: true,
		}

		await listCrossCodeDefinitionNamesTool(
			mockTask as Task,
			block,
			mockAskApproval,
			mockHandleError,
			mockPushToolResult,
			mockRemoveClosingTag,
		)

		expect(mockTask.ask).toHaveBeenCalled()
	})

	it("should generate cross file definitions successfully", async () => {
		const block: ToolUse = {
			type: "tool_use",
			name: "list_cross_code_definition_names",
			params: { path: "test.java" },
			partial: false,
		}

		await listCrossCodeDefinitionNamesTool(
			mockTask as Task,
			block,
			mockAskApproval,
			mockHandleError,
			mockPushToolResult,
			mockRemoveClosingTag,
		)

		expect(mockAskApproval).toHaveBeenCalled()
		expect(mockPushToolResult).toHaveBeenCalledWith("mock cross file definitions")
		expect(mockTask.consecutiveMistakeCount).toBe(0)
	})

	it("should handle approval denial", async () => {
		mockAskApproval.mockResolvedValue(false)

		const block: ToolUse = {
			type: "tool_use",
			name: "list_cross_code_definition_names",
			params: { path: "test.java" },
			partial: false,
		}

		await listCrossCodeDefinitionNamesTool(
			mockTask as Task,
			block,
			mockAskApproval,
			mockHandleError,
			mockPushToolResult,
			mockRemoveClosingTag,
		)

		expect(mockPushToolResult).not.toHaveBeenCalled()
	})
})
