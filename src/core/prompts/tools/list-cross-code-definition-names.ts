import { ToolArgs } from "./types"

export function getListCrossCodeDefinitionNamesDescription(args: ToolArgs): string {
	return `## list_cross_code_definition_names
Description: Request to list cross-file definition names from imported files. This tool analyzes a source file and provides definitions from all imported custom classes/modules, helping understand cross-file dependencies and relationships. It's particularly useful for unit testing scenarios where you need to understand the structure of imported dependencies.
Parameters:
- path: (required) The path of the source file (relative to the current working directory ${args.cwd}) to analyze for cross-file definitions.
Usage:
<list_cross_code_definition_names>
<path>File path here</path>
</list_cross_code_definition_names>

Examples:

1. List cross-file definitions for a Java controller:
<list_cross_code_definition_names>
<path>src/main/java/com/example/controller/UserController.java</path>
</list_cross_code_definition_names>

2. List cross-file definitions for a TypeScript service:
<list_cross_code_definition_names>
<path>src/services/UserService.ts</path>
</list_cross_code_definition_names>`
}
