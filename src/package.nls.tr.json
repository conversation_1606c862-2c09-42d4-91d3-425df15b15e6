{"extension.displayName": "<PERSON><PERSON><PERSON>:AI Coding Assistant-Intelligent partner in development", "extension.description": "<PERSON><PERSON><PERSON>:AI Coding Assistant-your intelligent partner in software development with automatic code generation", "command.newTask.title": "<PERSON><PERSON>", "command.explainCode.title": "Kodu Açıkla", "command.fixCode.title": "<PERSON><PERSON>", "command.improveCode.title": "<PERSON><PERSON>tir", "command.unitTest.title": "<PERSON><PERSON><PERSON>", "command.codeReview.title": "<PERSON><PERSON>", "command.commentCode.title": "Kodu Açıkla", "command.addToContext.title": "Bağ<PERSON><PERSON>", "command.openInNewTab.title": "<PERSON><PERSON>", "command.focusInput.title": "<PERSON><PERSON><PERSON>", "command.setCustomStoragePath.title": "<PERSON><PERSON>", "command.terminal.addToContext.title": "Terminal İçeriğini Bağlama Ekle", "command.terminal.fixCommand.title": "<PERSON><PERSON> <PERSON><PERSON>", "command.terminal.explainCommand.title": "Bu Komutu Açıkla", "command.acceptInput.title": "Girişi/Öneriyi Kabul Et", "views.activitybar.title": "<PERSON><PERSON><PERSON>", "views.contextMenu.label": "<PERSON><PERSON><PERSON>", "views.terminalMenu.label": "<PERSON><PERSON><PERSON>", "views.sidebar.name": "<PERSON><PERSON><PERSON>", "command.mcpServers.title": "MCP Sunucuları", "command.prompts.title": "<PERSON><PERSON><PERSON>", "command.history.title": "Geçmiş", "command.marketplace.title": "Marketplace", "command.openInEditor.title": "Düzenleyicide Aç", "command.settings.title": "<PERSON><PERSON><PERSON>", "command.documentation.title": "Dokümantasyon", "command.logout.title": "Çıkış yap", "configuration.title": "<PERSON><PERSON><PERSON>", "commands.allowedCommands.description": "'Her zaman yür<PERSON><PERSON><PERSON> işlemlerini onayla' etkinleştirildiğinde otomatik olarak yürütülebilen komutlar", "settings.vsCodeLmModelSelector.description": "VSCode dil modeli API'si için a<PERSON>", "settings.vsCodeLmModelSelector.vendor.description": "Dil modelinin <PERSON>ğlayıcısı (örn: copilot)", "settings.vsCodeLmModelSelector.family.description": "<PERSON>l modelinin a<PERSON> (örn: gpt-4)", "settings.customStoragePath.description": "Özel depolama yolu. Varsayılan konumu kullanmak için boş bırakın. Mutlak yolları destekler (örn: 'D:\\ZhanluStorage')", "settings.completion.enterprise_code_completion.description": "<PERSON><PERSON><PERSON> kodu tamamla", "settings.rooCodeCloudEnabled.description": "Ecloud Cloud'u Etkinleştir.", "settings.completion.debounce_time.description": "<PERSON><PERSON> tama<PERSON>a tetikleme gecikmesi mi<PERSON> (ms)", "settings.completion.completion_number.description": "Kod tama<PERSON>a i<PERSON>in o<PERSON>n aday sayısı", "settings.completion.inlineCompletion_granularity.description": "Sa<PERSON><PERSON>r içi tamamlama ayrıntı düzeyi tercihi", "settings.completion.inlineCompletion_granularity.singleRow": "Tek <PERSON>ır", "settings.completion.inlineCompletion_granularity.oneTimeMaximization": "Tek seferde maksimum", "settings.completion.inlineCompletion_granularity.balanced": "<PERSON><PERSON><PERSON>", "settings.completion.multiple_line_Completion.description": "Çok satırlı kod tamamlama yöntemi", "settings.completion.multiple_line_Completion.autoCompletion": "Otomatik tama<PERSON>a", "settings.completion.multiple_line_Completion.triggerCompletion": "<PERSON><PERSON><PERSON><PERSON> ile tama<PERSON>a", "settings.completion.multiple_line_Completion.autoCompletion.description": "Enter ve Ctrl+K (Mac'te cmd+K) çok satırlı tamamlamayı tetikleyebilir", "settings.completion.multiple_line_Completion.triggerCompletion.description": "Yalnızca Ctrl+K (Mac'te cmd+K) çok satırlı tamamlamayı tetikler, Enter tetiklemez", "settings.completion.max_tokens_completion.description": "Kod tama<PERSON>a i<PERSON>in maksimum token sayısı", "settings.dmt.maxTokens.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kodu çok modal soru ve cevap için max_tokens", "settings.serverBaseUrl.description": "<PERSON><PERSON><PERSON> sunucusunun temel URL'si, <PERSON><PERSON><PERSON><PERSON> https://api-wuxi-1.cmecloud.cn:8443'dir"}