{"extension.displayName": "<PERSON><PERSON><PERSON>:AI Coding Assistant-Intelligent partner in development", "extension.description": "<PERSON><PERSON><PERSON>:AI Coding Assistant-your intelligent partner in software development with automatic code generation", "command.newTask.title": "Nova Tarefa", "command.explainCode.title": "Explicar Código", "command.fixCode.title": "<PERSON><PERSON><PERSON><PERSON>", "command.improveCode.title": "<PERSON><PERSON><PERSON>", "command.unitTest.title": "Teste Unitário", "command.codeReview.title": "Revisão de código", "command.commentCode.title": "Comentar Código", "command.addToContext.title": "Adicionar ao Contexto", "command.openInNewTab.title": "Abrir em Nova Aba", "command.focusInput.title": "Focar Campo de Entrada", "command.setCustomStoragePath.title": "Definir Caminho de Armazenamento Personalizado", "command.terminal.addToContext.title": "Adicionar <PERSON>teúdo do Terminal ao Contexto", "command.terminal.fixCommand.title": "<PERSON><PERSON><PERSON><PERSON>", "command.terminal.explainCommand.title": "Explicar Este Comando", "command.acceptInput.title": "Aceitar Entrada/Sugestão", "views.activitybar.title": "<PERSON><PERSON><PERSON>", "views.contextMenu.label": "<PERSON><PERSON><PERSON>", "views.terminalMenu.label": "<PERSON><PERSON><PERSON>", "views.sidebar.name": "<PERSON><PERSON><PERSON>", "command.mcpServers.title": "Servidores MCP", "command.prompts.title": "Modos", "command.history.title": "Hist<PERSON><PERSON><PERSON>", "command.marketplace.title": "Marketplace", "command.openInEditor.title": "Abrir no Editor", "command.settings.title": "Configurações", "command.documentation.title": "Documentação", "command.logout.title": "<PERSON><PERSON>", "configuration.title": "<PERSON><PERSON><PERSON>", "commands.allowedCommands.description": "Comandos que podem ser executados automaticamente quando 'Sempre aprovar operações de execução' está ativado", "settings.vsCodeLmModelSelector.description": "Configurações para a API do modelo de linguagem do VSCode", "settings.vsCodeLmModelSelector.vendor.description": "O fornecedor do modelo de linguagem (ex: copilot)", "settings.vsCodeLmModelSelector.family.description": "A família do modelo de linguagem (ex: gpt-4)", "settings.customStoragePath.description": "Caminho de armazenamento personalizado. Deixe vazio para usar o local padrão. Suporta caminhos absolutos (ex: 'D:\\ZhanluStorage')", "settings.completion.enterprise_code_completion.description": "Completar código empresarial", "settings.rooCodeCloudEnabled.description": "Habilitar Ecloud Cloud.", "settings.completion.debounce_time.description": "Tempo de atraso em milissegundos (ms) para acionar a conclusão de código", "settings.completion.completion_number.description": "Número de candidatos gerados para conclusão de código", "settings.completion.inlineCompletion_granularity.description": "Preferência de granularidade para conclusão em linha", "settings.completion.inlineCompletion_granularity.singleRow": "Linha ú<PERSON>", "settings.completion.inlineCompletion_granularity.oneTimeMaximization": "Maximização única", "settings.completion.inlineCompletion_granularity.balanced": "Equilibrado", "settings.completion.multiple_line_Completion.description": "Método de conclusão para múltiplas linhas", "settings.completion.multiple_line_Completion.autoCompletion": "Conclusão automática", "settings.completion.multiple_line_Completion.triggerCompletion": "Conclusão acionada", "settings.completion.multiple_line_Completion.autoCompletion.description": "Enter e Ctrl+K (Cmd+K no Mac) podem acionar conclusão de múltiplas linhas", "settings.completion.multiple_line_Completion.triggerCompletion.description": "Apenas Ctrl+K (Cmd+K no Mac) aciona conclusão de múltiplas linhas, Enter não aciona", "settings.completion.max_tokens_completion.description": "max_tokens para conclusão de código", "settings.dmt.maxTokens.description": "max_tokens de código de imagem para perguntas e respostas multimodais", "settings.serverBaseUrl.description": "URL base para o servid<PERSON>, por padrão é https://api-wuxi-1.cmecloud.cn:8443"}