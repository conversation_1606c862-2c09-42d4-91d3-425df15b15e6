{"extension.displayName": "<PERSON><PERSON><PERSON>:AI Coding Assistant-Intelligent partner in development", "extension.description": "<PERSON><PERSON><PERSON>:AI Coding Assistant-your intelligent partner in software development with automatic code generation", "command.newTask.title": "새 작업", "command.explainCode.title": "코드 설명", "command.fixCode.title": "코드 수정", "command.improveCode.title": "코드 개선", "command.unitTest.title": "단위 테스트", "command.codeReview.title": "코드 검토", "command.commentCode.title": "코드 주석 추가", "command.addToContext.title": "컨텍스트에 추가", "command.openInNewTab.title": "새 탭에서 열기", "command.focusInput.title": "입력 필드 포커스", "command.setCustomStoragePath.title": "사용자 지정 저장소 경로 설정", "command.terminal.addToContext.title": "터미널 내용을 컨텍스트에 추가", "command.terminal.fixCommand.title": "이 명령어 수정", "command.terminal.explainCommand.title": "이 명령어 설명", "command.acceptInput.title": "입력/제안 수락", "views.activitybar.title": "<PERSON><PERSON><PERSON>", "views.contextMenu.label": "<PERSON><PERSON><PERSON>", "views.terminalMenu.label": "<PERSON><PERSON><PERSON>", "views.sidebar.name": "<PERSON><PERSON><PERSON>", "command.mcpServers.title": "MCP 서버", "command.prompts.title": "모드", "command.history.title": "기록", "command.marketplace.title": "마켓플레이스", "command.openInEditor.title": "에디터에서 열기", "command.settings.title": "설정", "command.documentation.title": "문서", "command.logout.title": "로그아웃", "configuration.title": "<PERSON><PERSON><PERSON>", "commands.allowedCommands.description": "'항상 실행 작업 승인' 이 활성화되어 있을 때 자동으로 실행할 수 있는 명령어", "settings.vsCodeLmModelSelector.description": "VSCode 언어 모델 API 설정", "settings.vsCodeLmModelSelector.vendor.description": "언어 모델 공급자 (예: copilot)", "settings.vsCodeLmModelSelector.family.description": "언어 모델 계열 (예: gpt-4)", "settings.customStoragePath.description": "사용자 지정 저장소 경로. 기본 위치를 사용하려면 비워두세요. 절대 경로를 지원합니다 (예: 'D:\\ZhanluStorage')", "settings.completion.enterprise_code_completion.description": "기업 코드 완성", "settings.rooCodeCloudEnabled.description": "Ecloud Cloud 사용 설정", "settings.completion.debounce_time.description": "코드 완성 트리거 지연 시간(ms)", "settings.completion.completion_number.description": "코드 완성 후보 생성 개수", "settings.completion.inlineCompletion_granularity.description": "완성 세분화 선호도", "settings.completion.inlineCompletion_granularity.singleRow": "단일 행", "settings.completion.inlineCompletion_granularity.oneTimeMaximization": "일회성 최대화", "settings.completion.inlineCompletion_granularity.balanced": "균형", "settings.completion.multiple_line_Completion.description": "다중 행 코드 완성 방식", "settings.completion.multiple_line_Completion.autoCompletion": "자동 완성", "settings.completion.multiple_line_Completion.triggerCompletion": "트리거 완성", "settings.completion.multiple_line_Completion.autoCompletion.description": "enter 및 Ctrl+K(맥에서는 cmd+k) 모두 다중 행 완성 트리거 가능", "settings.completion.multiple_line_Completion.triggerCompletion.description": "Ctrl+K(맥에서는 cmd+k)만 다중 행 완성 트리거, enter는 트리거 안함", "settings.completion.max_tokens_completion.description": "코드 완성의 max_tokens", "settings.dmt.maxTokens.description": "그림 생성 코드 다중 모드 문답의 max_tokens", "settings.serverBaseUrl.description": "<PERSON><PERSON><PERSON> 서버의 기본 URL, 기본값은 https://api-wuxi-1.cmecloud.cn:8443"}