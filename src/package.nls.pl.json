{"extension.displayName": "<PERSON><PERSON><PERSON>:AI Coding Assistant-Intelligent partner in development", "extension.description": "<PERSON><PERSON><PERSON>:AI Coding Assistant-your intelligent partner in software development with automatic code generation", "command.newTask.title": "Nowe Zadanie", "command.explainCode.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "command.fixCode.title": "<PERSON><PERSON><PERSON>", "command.improveCode.title": "Ulepsz Kod", "command.unitTest.title": "Test Jednostkowy", "command.codeReview.title": "Przegląd kodu", "command.commentCode.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "command.addToContext.title": "Dodaj do Kontekstu", "command.openInNewTab.title": "Otwórz w Nowej Karcie", "command.focusInput.title": "Fokus na Pole Wprowadzania", "command.setCustomStoragePath.title": "Ustaw Niestandardową Ścieżkę Przechowywania", "command.terminal.addToContext.title": "Dodaj <PERSON>ć Terminala do Kontekstu", "command.terminal.fixCommand.title": "Napraw tę <PERSON>", "command.terminal.explainCommand.title": "Wyjaśnij tę Komendę", "command.acceptInput.title": "Akceptuj Wprowadzanie/Sugestię", "views.activitybar.title": "<PERSON><PERSON><PERSON>", "views.contextMenu.label": "<PERSON><PERSON><PERSON>", "views.terminalMenu.label": "<PERSON><PERSON><PERSON>", "views.sidebar.name": "<PERSON><PERSON><PERSON>", "command.mcpServers.title": "Serwery MCP", "command.prompts.title": "Tryby", "command.history.title": "Historia", "command.marketplace.title": "Marketplace", "command.openInEditor.title": "Otwórz w Edytorze", "command.settings.title": "Ustawienia", "command.documentation.title": "Dokumentacja", "command.logout.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "configuration.title": "<PERSON><PERSON><PERSON>", "commands.allowedCommands.description": "Polecenia, które mogą być wykonywane automatycznie, gdy włączona jest opcja '<PERSON><PERSON><PERSON> zatwierdzaj operacje wykonania'", "settings.vsCodeLmModelSelector.description": "Ustawienia dla API modelu językowego VSCode", "settings.vsCodeLmModelSelector.vendor.description": "Dostawca modelu językowego (np. copilot)", "settings.vsCodeLmModelSelector.family.description": "Rodzina modelu językowego (np. gpt-4)", "settings.customStoragePath.description": "Niestandardowa ścieżka przechowywania. Pozostaw puste, aby użyć domyślnej lokalizacji. Obsługuje ścieżki bezwzględne (np. 'D:\\ZhanluStorage')", "settings.completion.enterprise_code_completion.description": "Uzupełnij kod biznesowy", "settings.rooCodeCloudEnabled.description": "Włącz Ecloud Cloud.", "settings.completion.debounce_time.description": "Opóźnienie wyzwalania uzupełniania kodu w milisekundach (ms)", "settings.completion.completion_number.description": "Liczba kandydatów do uzupełnienia kodu", "settings.completion.inlineCompletion_granularity.description": "Preferencje dotyczące szczegółowości uzupełniania", "settings.completion.inlineCompletion_granularity.singleRow": "Poje<PERSON><PERSON><PERSON> wiersz", "settings.completion.inlineCompletion_granularity.oneTimeMaximization": "Ma<PERSON><PERSON>ali<PERSON><PERSON> jednorazowa", "settings.completion.inlineCompletion_granularity.balanced": "Zrównoważony", "settings.completion.multiple_line_Completion.description": "Metoda uzupełniania wielu linii kodu", "settings.completion.multiple_line_Completion.autoCompletion": "Automatyczne uzupełnianie", "settings.completion.multiple_line_Completion.triggerCompletion": "Uzupełnianie wyzwalane", "settings.completion.multiple_line_Completion.autoCompletion.description": "Enter i Ctrl+K (cmd+k w systemie macOS) mogą wyzwalać uzupełnianie wielu linii", "settings.completion.multiple_line_Completion.triggerCompletion.description": "Tylko Ctrl+K (cmd+k w systemie macOS) wyzwala uzupełnianie wielu linii, enter nie wyzwala", "settings.completion.max_tokens_completion.description": "Maksymalna liczba tokenów dla uzupełniania kodu (max_tokens)", "settings.dmt.maxTokens.description": "max_tokens dla wielomodowych pytań i odpowiedzi", "settings.serverBaseUrl.description": "URL podstawowy dla ser<PERSON>, do<PERSON><PERSON><PERSON><PERSON> jest https://api-wuxi-1.cmecloud.cn:8443"}