{"extension.displayName": "<PERSON><PERSON><PERSON>:AI Coding Assistant-Intelligent partner in development", "extension.description": "<PERSON><PERSON><PERSON>:AI Coding Assistant-your intelligent partner in software development with automatic code generation", "command.newTask.title": "新建任務", "command.explainCode.title": "解釋程式碼", "command.fixCode.title": "修復程式碼", "command.improveCode.title": "改進程式碼", "command.unitTest.title": "單元測試", "command.codeReview.title": "程式碼評審", "command.commentCode.title": "評論程式碼", "command.addToContext.title": "添加到上下文", "command.openInNewTab.title": "在新分頁中開啟", "command.focusInput.title": "聚焦輸入框", "command.setCustomStoragePath.title": "設定自訂儲存路徑", "command.terminal.addToContext.title": "將終端內容新增到上下文", "command.terminal.fixCommand.title": "修復此命令", "command.terminal.explainCommand.title": "解釋此命令", "command.acceptInput.title": "接受輸入/建議", "views.activitybar.title": "<PERSON><PERSON><PERSON>", "views.contextMenu.label": "<PERSON><PERSON><PERSON>", "views.terminalMenu.label": "<PERSON><PERSON><PERSON>", "views.sidebar.name": "<PERSON><PERSON><PERSON>", "command.mcpServers.title": "MCP 伺服器", "command.prompts.title": "模式", "command.history.title": "歷史記錄", "command.marketplace.title": "應用市場", "command.openInEditor.title": "在編輯器中開啟", "command.settings.title": "設定", "command.documentation.title": "文件", "command.logout.title": "登出", "configuration.title": "<PERSON><PERSON><PERSON>", "commands.allowedCommands.description": "當啟用'始終批准執行操作'時可以自動執行的命令", "settings.vsCodeLmModelSelector.description": "VSCode 語言模型 API 的設定", "settings.vsCodeLmModelSelector.vendor.description": "語言模型的供應商（例如：copilot）", "settings.vsCodeLmModelSelector.family.description": "語言模型的系列（例如：gpt-4）", "settings.customStoragePath.description": "自訂儲存路徑。留空以使用預設位置。支援絕對路徑（例如：'D:\\ZhanluStorage'）", "settings.completion.debounce_time.description": "代碼補全觸發時延毫秒(ms)", "settings.completion.completion_number.description": "代碼補全生成候選個數", "settings.completion.inlineCompletion_granularity.description": "補全粒度的偏好", "settings.completion.inlineCompletion_granularity.singleRow": "單行", "settings.completion.inlineCompletion_granularity.oneTimeMaximization": "一次性最大化", "settings.completion.inlineCompletion_granularity.balanced": "均衡", "settings.completion.multiple_line_Completion.description": "多行代碼補全方式", "settings.completion.multiple_line_Completion.autoCompletion": "自動補全", "settings.completion.multiple_line_Completion.triggerCompletion": "觸發補全", "settings.completion.multiple_line_Completion.autoCompletion.description": "enter及Ctrl+K(蘋果系統為cmd+k)均可觸發多行補全", "settings.completion.multiple_line_Completion.triggerCompletion.description": "僅Ctrl+K(蘋果系統為cmd+k)觸發多行補全，enter不觸發", "settings.completion.max_tokens_completion.description": "代碼補全的max_tokens", "settings.dmt.maxTokens.description": "圖生程式碼多模態問答的max_tokens", "settings.serverBaseUrl.description": "湛盧伺服器的基礎 URL 地址，預設為 https://api-wuxi-1.cmecloud.cn:8443"}