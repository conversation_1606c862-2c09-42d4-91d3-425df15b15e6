{"extension.displayName": "<PERSON><PERSON><PERSON>", "extension.description": "Un equip complet de desenvolupament d'agents d'IA al teu editor.", "command.newTask.title": "Nova Tasca", "command.explainCode.title": "Explicar Codi", "command.fixCode.title": "<PERSON><PERSON><PERSON><PERSON>", "command.improveCode.title": "Millorar Codi", "command.unitTest.title": "Test Unitari", "command.codeReview.title": "Revisió de codi", "command.commentCode.title": "Comentar codi", "command.addToContext.title": "<PERSON><PERSON><PERSON>r al Context", "command.openInNewTab.title": "Obrir en una Nova Pestanya", "command.focusInput.title": "Enfocar Camp d'Entrada", "command.setCustomStoragePath.title": "Establir Ruta d'Emmagatzematge Personalitzada", "command.terminal.addToContext.title": "Afegir Contingut del Terminal al Context", "command.terminal.fixCommand.title": "<PERSON><PERSON><PERSON><PERSON>", "command.terminal.explainCommand.title": "Explicar <PERSON><PERSON><PERSON>", "command.acceptInput.title": "Acceptar Entrada/Suggeriment", "views.activitybar.title": "<PERSON><PERSON><PERSON>", "views.contextMenu.label": "<PERSON><PERSON><PERSON>", "views.terminalMenu.label": "<PERSON><PERSON><PERSON>", "views.sidebar.name": "<PERSON><PERSON><PERSON>", "command.mcpServers.title": "Servidors MCP", "command.prompts.title": "Modes", "command.history.title": "Historial", "command.marketplace.title": "Mercat", "command.openInEditor.title": "Obrir a l'Editor", "command.settings.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "command.documentation.title": "Documentació", "configuration.title": "<PERSON><PERSON><PERSON>", "commands.allowedCommands.description": "Ordres que es poden executar automàticament quan 'Aprova sempre les operacions d'execució' està activat", "settings.vsCodeLmModelSelector.description": "Configuració per a l'API del model de llenguatge VSCode", "settings.vsCodeLmModelSelector.vendor.description": "El proveïdor del model de llenguatge (p. ex. copilot)", "settings.vsCodeLmModelSelector.family.description": "La família del model de llenguatge (p. ex. gpt-4)", "settings.customStoragePath.description": "Ruta d'emmagatzematge personalitzada. Deixeu-la buida per utilitzar la ubicació predeterminada. Admet rutes absolutes (p. ex. 'D:\\ZhanluStorage')", "settings.completion.debounce_time.description": "Temps de retard en mil·lisegons (ms) per activar la compleció de codi", "settings.completion.completion_number.description": "Nombre de candidats generats per a la compleció de codi", "settings.completion.inlineCompletion_granularity.description": "Preferència de granularitat de compleció", "settings.completion.inlineCompletion_granularity.singleRow": "Línia única", "settings.completion.inlineCompletion_granularity.oneTimeMaximization": "Maximització d'una sola vegada", "settings.completion.inlineCompletion_granularity.balanced": "Equilibrat", "settings.completion.multiple_line_Completion.description": "Mètode de compleció de codi de múltiples línies", "settings.completion.multiple_line_Completion.autoCompletion": "Compleció automàtica", "settings.completion.multiple_line_Completion.triggerCompletion": "Compleció activada", "settings.completion.multiple_line_Completion.autoCompletion.description": "Tant enter com Ctrl+K (Cmd+K a Mac) poden activar la compleció de múltiples línies", "settings.completion.multiple_line_Completion.triggerCompletion.description": "Només Ctrl+K (Cmd+K a Mac) activa la compleció de múltiples línies, enter no l'activa", "settings.completion.max_tokens_completion.description": "max_tokens per a la compleció de codi", "settings.dmt.maxTokens.description": "max_tokens per a qüestions i respostes multimodals", "settings.serverBaseUrl.description": "URL base per al servidor <PERSON>, per defecte <PERSON>s https://api-wuxi-1.cmecloud.cn:8443"}