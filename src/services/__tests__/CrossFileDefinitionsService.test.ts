import { describe, it, expect, beforeEach } from "vitest"
import { CrossFileDefinitionsService } from "../CrossFileDefinitionsService"

describe("CrossFileDefinitionsService", () => {
	let service: CrossFileDefinitionsService

	beforeEach(() => {
		service = new CrossFileDefinitionsService()
	})

	describe("generateCrossFileDefinitions", () => {
		it("should return empty string for non-existent file", async () => {
			const result = await service.generateCrossFileDefinitions("/non/existent/file.java")
			expect(result).toBe("")
		})

		it("should handle files without imports", async () => {
			// 这个测试需要实际的文件系统支持
			// 在实际环境中，这里应该创建一个没有导入的测试文件
			const result = await service.generateCrossFileDefinitions("test-file-without-imports.java")
			expect(typeof result).toBe("string")
		})

		it("should use caching correctly", async () => {
			const service = new CrossFileDefinitionsService()

			// 第一次调用
			const result1 = await service.generateCrossFileDefinitions("/test/file.java")

			// 第二次调用应该使用缓存
			const result2 = await service.generateCrossFileDefinitions("/test/file.java")

			expect(result1).toBe(result2)
		})
	})

	describe("service initialization", () => {
		it("should create service instance", () => {
			expect(service).toBeDefined()
		})

		it("should have generateCrossFileDefinitions method", () => {
			expect(typeof service.generateCrossFileDefinitions).toBe("function")
		})
	})
})
