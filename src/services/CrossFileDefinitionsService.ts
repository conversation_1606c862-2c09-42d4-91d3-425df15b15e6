import * as vscode from "vscode"
import { LRUCache } from "lru-cache"

import { ImportDefinitionsService } from "../cross-file/ImportDefinitionsService"
import { getWorkspacePath, getReadablePath } from "../utils/path"
import { parseSourceCodeDefinitionsForFile } from "./tree-sitter"

/**
 * 跨文件定义服务
 * 负责生成跨文件感知内容，类似list_code_definition_names但包含类成员属性
 */
export class CrossFileDefinitionsService {
	// 跨文件感知内容缓存，最多保留20条记录
	private crossFileDefinitionsCache = new LRUCache<string, string>({
		max: 20,
	})

	private importDefinitionsService: ImportDefinitionsService

	constructor() {
		this.importDefinitionsService = new ImportDefinitionsService()
	}

	/**
	 * 生成跨文件感知内容，类似list_code_definition_names但包含类成员属性
	 * @param filePath 待测文件路径
	 * @returns 跨文件感知内容字符串
	 */
	async generateCrossFileDefinitions(filePath: string): Promise<string> {
		try {
			// 检查缓存
			const cached = this.crossFileDefinitionsCache.get(filePath)
			if (cached) {
				console.debug(`Using cached cross file definitions for ${filePath}`)
				return cached
			}

			const uri = vscode.Uri.file(filePath)
			const crossFileInfo = await this.importDefinitionsService.get(uri)

			if (!crossFileInfo || !Object.keys(crossFileInfo.imports).length) {
				console.debug(`No cross file imports found for ${filePath}`)
				return ""
			}

			let result = ""
			let totalImports = 0
			let successfulImports = 0

			// 统计总导入数量
			for (const rangeInFiles of Object.values(crossFileInfo.imports)) {
				totalImports += rangeInFiles.length
			}

			console.debug(`Found ${totalImports} cross file imports for ${filePath}`)

			// 获取工作空间根目录作为基准路径
			const workspaceRoot = getWorkspacePath()

			// 遍历所有导入的类文件，最多20条
			let count = 0
			for (const [importName, rangeInFiles] of Object.entries(crossFileInfo.imports)) {
				if (count >= 20) break

				for (const rangeInFile of rangeInFiles) {
					if (count >= 20) break

					try {
						// 生成类似list_code_definition_names的内容，但包含类成员属性
						const definitions = await this.generateDefinitionsWithMembers(rangeInFile.filepath)
						if (definitions) {
							// 使用项目标准的getReadablePath函数来获取相对路径
							const relativePath = getReadablePath(workspaceRoot, rangeInFile.filepath)
							result += `[list_cross_code_definition_names for '${relativePath}'] Result:\n${definitions}\n\n`
							count++
							successfulImports++
							console.debug(`Successfully generated definitions for ${importName} -> ${relativePath}`)
						} else {
							const relativePath = getReadablePath(workspaceRoot, rangeInFile.filepath)
							console.debug(`No definitions generated for ${importName} -> ${relativePath}`)
						}
					} catch (error) {
						const relativePath = getReadablePath(workspaceRoot, rangeInFile.filepath)
						console.warn(`Failed to generate definitions for ${importName} -> ${relativePath}:`, error)
					}
				}
			}

			console.debug(`Generated cross file definitions: ${successfulImports}/${totalImports} successful`)

			// 缓存结果
			if (result) {
				this.crossFileDefinitionsCache.set(filePath, result)
			}

			return result
		} catch (error) {
			console.warn(`Failed to generate cross file definitions for ${filePath}:`, error)
			return ""
		}
	}

	/**
	 * 生成包含类成员属性的定义内容
	 * @param filePath 文件路径 (可能是file://开头的URI或绝对路径)
	 * @returns 定义内容字符串
	 */
	private async generateDefinitionsWithMembers(filePath: string): Promise<string> {
		try {
			// 直接使用parseSourceCodeDefinitionsForFile方法，它已经正确处理了作用域范围
			const definitions = await parseSourceCodeDefinitionsForFile(filePath)
			if (!definitions) {
				return ""
			}

			// parseSourceCodeDefinitionsForFile已经返回了正确格式的定义，包括正确的行号范围
			// 格式：# filename\nstartLine--endLine | content\n...
			return definitions
		} catch (error) {
			console.warn(`Failed to generate definitions with members for ${filePath}:`, error)
			return ""
		}
	}
}
