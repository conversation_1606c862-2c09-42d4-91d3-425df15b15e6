/*
Query patterns for Java language structures
*/
export default `
; Module declarations
(module_declaration
  name: (scoped_identifier) @name.definition.module) @definition.module

; Package declarations
((package_declaration
  (scoped_identifier)) @name.definition.package) @definition.package

; Line comments
(line_comment) @definition.comment

; Class declarations (including annotated classes)
(class_declaration
  (modifiers)?
  name: (identifier) @name.definition.class) @definition.class

; Annotated class declarations (capture annotations separately)
(class_declaration
  (modifiers
    (annotation
      name: (identifier) @name.definition.annotation))
  name: (identifier) @name.definition.annotated_class) @definition.annotated_class

; Interface declarations
(interface_declaration
  name: (identifier) @name.definition.interface) @definition.interface

; Enum declarations
(enum_declaration
  name: (identifier) @name.definition.enum) @definition.enum

; Record declarations
(record_declaration
  name: (identifier) @name.definition.record) @definition.record

; Annotation declarations
(annotation_type_declaration
  name: (identifier) @name.definition.annotation) @definition.annotation

; Constructor declarations (including annotated constructors)
(constructor_declaration
  (modifiers)?
  name: (identifier) @name.definition.constructor) @definition.constructor

; Annotated constructor declarations (capture annotations separately)
(constructor_declaration
  (modifiers
    (annotation
      name: (identifier) @name.definition.annotation))
  name: (identifier) @name.definition.annotated_constructor) @definition.annotated_constructor

; Method declarations - capture only the signature, not the body
(method_declaration
  (modifiers)?
  type: (_)?
  name: (identifier) @name.definition.method
  parameters: (formal_parameters)) @definition.method

; Annotated method declarations (capture annotations separately)
(method_declaration
  (modifiers
    (annotation
      name: (identifier) @name.definition.annotation))
  type: (_)?
  name: (identifier) @name.definition.annotated_method
  parameters: (formal_parameters)) @definition.annotated_method

; Inner class declarations
(class_declaration
  (class_body
    (class_declaration
      name: (identifier) @name.definition.inner_class))) @definition.inner_class

; Static nested class declarations
(class_declaration
  (class_body
    (class_declaration
      name: (identifier) @name.definition.static_nested_class))) @definition.static_nested_class

; Lambda expressions
(lambda_expression) @definition.lambda

; Field declarations (all fields including private, public, etc.)
(field_declaration
  (modifiers)?
  type: (_)
  declarator: (variable_declarator
    name: (identifier) @name.definition.field)) @definition.field

; Annotated field declarations (capture annotations separately)
(field_declaration
  (modifiers
    (annotation
      name: (identifier) @name.definition.annotation))
  type: (_)
  declarator: (variable_declarator
    name: (identifier) @name.definition.annotated_field)) @definition.annotated_field

; Single annotation on field (for cases like @Value)
(field_declaration
  (modifiers
    (annotation) @definition.field_annotation)
  type: (_)
  declarator: (variable_declarator
    name: (identifier) @name.definition.field)) @definition.field

; Import declarations
(import_declaration
  (scoped_identifier) @name.definition.import) @definition.import

; Type parameters
(type_parameters
  (type_parameter) @name.definition.type_parameter) @definition.type_parameter
`
