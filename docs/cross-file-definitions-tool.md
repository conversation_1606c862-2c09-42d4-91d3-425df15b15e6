# 跨文件感知工具优化 (Cross-File Definitions Tool Enhancement)

## 概述

本次优化将原本集成在 `ImportDefinitionsService` 中的跨文件感知功能抽离为独立的工具 `list_cross_code_definition_names`，提升了代码的可维护性和架构清晰度。

## 功能说明

### 原有功能

- **自动触发**: 在单测智能体模式下，使用 `read_file` 工具读取单个文件时自动触发跨文件感知
- **跨文件分析**: 分析待测文件中所有 import 的自定义类文件，生成类似 `list_code_definition_names` 的内容但包含类成员属性
- **缓存机制**: 文件级缓存最多20条，历史会话缓存最多保留最近3次

### 新增功能

- **独立工具调用**: 用户可以主动调用 `list_cross_code_definition_names` 工具
- **统一工具体验**: 与现有工具保持一致的使用方式和错误处理

## 架构优化

### 优化前架构

```
ImportDefinitionsService
├── 导入分析功能
├── 跨文件感知生成功能 (generateCrossFileDefinitions)
├── 定义内容生成功能 (generateDefinitionsWithMembers)
└── 跨文件感知缓存

readFileTool
└── 直接调用 ImportDefinitionsService.generateCrossFileDefinitions
```

### 优化后架构

```
ImportDefinitionsService
└── 导入分析功能 (核心职责)

CrossFileDefinitionsService
├── 跨文件感知生成功能
├── 定义内容生成功能
└── 跨文件感知缓存

listCrossCodeDefinitionNamesTool
└── 独立工具接口

readFileTool
└── 调用 CrossFileDefinitionsService
```

## 文件变更

### 新增文件

- `src/core/tools/listCrossCodeDefinitionNamesTool.ts` - 新工具实现
- `src/core/prompts/tools/list-cross-code-definition-names.ts` - 工具描述
- `src/services/CrossFileDefinitionsService.ts` - 专用服务
- `src/services/__tests__/CrossFileDefinitionsService.test.ts` - 服务测试
- `src/core/tools/__tests__/listCrossCodeDefinitionNamesTool.test.ts` - 工具测试

### 修改文件

- `packages/types/src/tool.ts` - 添加工具名称定义
- `src/shared/tools.ts` - 添加工具接口、显示名称和分组
- `src/shared/ExtensionMessage.ts` - 添加工具类型定义
- `src/core/prompts/tools/index.ts` - 添加描述映射
- `src/core/assistant-message/presentAssistantMessage.ts` - 添加调用分发
- `src/cross-file/ImportDefinitionsService.ts` - 精简代码，移除跨文件感知生成逻辑
- `src/core/tools/readFileTool.ts` - 更新服务调用
- `src/cross-file/__tests__/ImportDefinitionsService.test.ts` - 更新测试
- `src/cross-file/__tests__/ImportDefinitionsService.enhanced.test.ts` - 更新测试

## 使用方式

### 1. 独立工具调用

```xml
<list_cross_code_definition_names>
<path>src/main/java/com/example/controller/UserController.java</path>
</list_cross_code_definition_names>
```

### 2. 自动集成 (单测智能体模式)

在单测智能体模式下使用 `read_file` 工具时，系统会自动追加跨文件感知内容：

```xml
<read_file>
<path>src/main/java/com/example/controller/UserController.java</path>
</read_file>
```

输出格式：

```
[文件内容]

[list_cross_code_definition_names for 'src/main/java/com/example/model/UserModel.java'] Result:
# UserModel.java
1--50 | public class UserModel {
    private String name;
    private String email;
    // ... 其他成员
}

[list_cross_code_definition_names for 'src/main/java/com/example/service/DataService.java'] Result:
# DataService.java
1--30 | public class DataService {
    public Map<String, Object> processUserData(UserModel user) {
        // ... 方法实现
    }
}
```

## 技术细节

### 缓存策略

- **文件级缓存**: 使用 LRU 缓存，最多保留20条跨文件感知记录
- **历史缓存**: Task 级别缓存，最多保留最近3次跨文件感知内容

### 错误处理

- 文件不存在时返回空字符串
- 无导入时返回空字符串
- 解析错误时记录警告并返回错误信息

### 性能优化

- 最多分析20个导入文件，避免过度分析
- 使用缓存减少重复计算
- 异步处理，不阻塞主流程

## 测试覆盖

### 单元测试

- `CrossFileDefinitionsService.test.ts`: 服务核心功能测试
- `listCrossCodeDefinitionNamesTool.test.ts`: 工具接口测试
- `ImportDefinitionsService.test.ts`: 更新后的导入分析测试

### 集成测试

- 在单测智能体模式下的自动触发测试
- 独立工具调用的端到端测试

## 兼容性

### 向后兼容

- 所有原有功能保持不变
- 现有的缓存机制继续有效
- 历史会话中的跨文件感知内容格式保持一致

### 升级影响

- 无需修改现有代码
- 无需更新配置文件
- 用户体验无变化

## 维护指南

### 添加新语言支持

1. 在 `CrossFileDefinitionsService` 中扩展语言识别逻辑
2. 更新 `parseSourceCodeDefinitionsForFile` 方法支持新语言
3. 添加相应的测试用例

### 性能调优

- 调整缓存大小: 修改 `LRUCache` 的 `max` 参数
- 调整分析文件数量限制: 修改循环中的 `count >= 20` 条件
- 优化解析逻辑: 在 `generateDefinitionsWithMembers` 方法中优化

### 故障排查

- 检查控制台日志中的 debug 和 warn 信息
- 验证文件路径是否正确
- 确认工作空间配置是否正确

## 未来规划

1. **智能过滤**: 根据使用频率智能过滤导入文件
2. **增量更新**: 支持文件变更时的增量更新
3. **多语言优化**: 针对不同语言的特定优化
4. **可视化**: 提供跨文件依赖关系的可视化界面
