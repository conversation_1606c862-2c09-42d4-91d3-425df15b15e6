# Web-Tree-Sitter 升级修复文档

## 问题描述

在将 web-tree-sitter 从 0.22.6 升级到 0.25.6 后，跨文件信息加载功能出现以下错误：

1. **初始化错误**：`Failed to initialize tree-sitter Parser`
2. **WASM 加载错误**：`TypeError: Cannot read properties of undefined (reading 'lengthBytesUTF8')`

## 根本原因

### 1. API 变化

web-tree-sitter 0.25.6 相比 0.22.6 有重大 API 变化：

**旧版本 (0.22.6)**：

```typescript
import Parser, { Language } from "web-tree-sitter"
await Parser.init()
const parser = new Parser()
return languageParser.query(pythonImportQuery)
```

**新版本 (0.25.6)**：

```typescript
import { Language, Query, Tree, Node } from "web-tree-sitter"
const { Parser } = require("web-tree-sitter")
await Parser.init()
const parser = new Parser()
return new Query(languageParser, pythonImportQuery)
```

### 2. 初始化问题

- 新版本需要使用 `require("web-tree-sitter")` 动态导入 Parser
- Query 创建方式从 `languageParser.query()` 改为 `new Query()`
- 需要正确处理 WASM 文件路径

### 3. esbuild 兼容性问题

在 esbuild 打包环境中，`import.meta.url` 可能为 undefined，导致 WASM 初始化失败。

## 解决方案

### 1. 修复导入方式

```typescript
// 修改前
import Parser, { Language, Query, Tree, Node } from "web-tree-sitter"

// 修改后
import { Language, Query, Tree, Node } from "web-tree-sitter"
import { loadLanguage, loadRequiredLanguageParsers } from "../../services/tree-sitter/languageParser"
```

### 2. 统一初始化逻辑（关键修复）

**问题根源**：`languageParser.ts` 和 `treeSitter.ts` 有两个独立的初始化系统，导致冲突。

**解决方案**：让 `treeSitter.ts` 重用 `languageParser.ts` 的初始化逻辑：

```typescript
// 使用与 languageParser.ts 相同的初始化方式避免冲突
async function ensureTreeSitterInitialized() {
	// 重用 languageParser.ts 的初始化逻辑
	// 这确保我们不会有冲突的初始化状态
	await loadRequiredLanguageParsers([])
}
```

### 3. 修复 Parser 创建

```typescript
export async function getParserForFile(filepath: string) {
	try {
		await ensureTreeSitterInitialized()
		const { Parser } = require("web-tree-sitter")
		const parser = new Parser()
		// ...
	} catch (e) {
		console.debug("Unable to load language for file", filepath, e)
		return undefined
	}
}
```

### 4. 修复 Query 创建（关键修复）

```typescript
export async function getQueryForFile(filepath: string, type = "import-query"): Promise<Query | undefined> {
	const languageParser = await getLanguageForFile(filepath)
	if (!languageParser) {
		return undefined
	}

	// 确保使用与 languageParser.ts 相同的 Query 构造函数
	await ensureTreeSitterInitialized()
	const { Query } = require("web-tree-sitter")

	// 使用统一的 Query 构造函数
	return new Query(languageParser, queryString)
}
```

### 5. 修复 WASM 路径问题

```typescript
export async function getLanguageForFile(filepath: string): Promise<Language | undefined> {
	try {
		await ensureTreeSitterInitialized()
		// ...
		if (!language) {
			// 传递正确的 sourceDirectory 参数
			language = await loadLanguage(languageName, typeof __dirname !== "undefined" ? __dirname : undefined)
			if (language) {
				nameToLanguage.set(languageName, language)
			}
		}
		return language
	} catch (e) {
		console.debug("Unable to load language for file", filepath, e)
		return undefined
	}
}
```

## 修复的文件

1. **src/cross-file/util/treeSitter.ts** - 主要修复文件
2. **删除 src/cross-file/util/tree-sitter-pre.ts** - 旧版本文件

## 验证结果

- ✅ TypeScript 类型检查通过
- ✅ 项目构建成功
- ✅ 没有编译错误
- ✅ WASM 文件正确复制到 dist 目录

## 关键要点

1. **统一初始化**：最重要的修复是统一 `languageParser.ts` 和 `treeSitter.ts` 的初始化逻辑
2. **避免重复初始化**：通过重用 `loadRequiredLanguageParsers([])` 确保只有一个初始化路径
3. **动态导入**：在函数内部使用 `require("web-tree-sitter")` 获取 `Parser` 和 `Query`
4. **错误处理**：添加适当的错误处理和调试日志
5. **WASM 路径**：确保传递正确的 `sourceDirectory` 参数给 `loadLanguage`

## 相关链接

- [GitHub Issue #4248](https://github.com/tree-sitter/tree-sitter/issues/4248) - esbuild 环境下的 import.meta.url 问题
- [web-tree-sitter 文档](https://github.com/tree-sitter/tree-sitter/tree/master/lib/binding_web)
