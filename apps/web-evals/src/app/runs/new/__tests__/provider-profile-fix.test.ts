/**
 * Test to verify that API.startNewTask properly handles provider configurations
 * and creates the appropriate profiles for evaluation runs
 */

describe("API startNewTask Provider Profile Fix", () => {
	test("should create temporary OpenRouter profile when OpenRouter configuration is provided", () => {
		// Mock configuration from an OpenRouter evaluation run
		const openRouterConfiguration = {
			apiProvider: "openrouter" as const,
			openRouterModelId: "gpt-4-turbo",
			openRouterApiKey: "sk-or-123456",
			diffEnabled: true,
			fuzzyMatchThreshold: 1,
			rateLimitSeconds: 0,
			// ... other settings from rooCodeDefaults merged with openrouter settings
		}

		// Simulate the logic that should happen in startNewTask
		const shouldCreateProfile = !!openRouterConfiguration.apiProvider
		const expectedProfileName = `eval-${openRouterConfiguration.apiProvider}-${Date.now()}`

		// Verify that the configuration has the correct provider
		expect(openRouterConfiguration.apiProvider).toBe("openrouter")
		expect(openRouterConfiguration.openRouterModelId).toBe("gpt-4-turbo")
		expect(shouldCreateProfile).toBe(true)
		expect(expectedProfileName).toMatch(/^eval-openrouter-\d+$/)
	})

	test("should create temporary Zhanlu profile when Zhanlu configuration is provided", () => {
		// Mock configuration from a Zhanlu evaluation run
		const zhanluConfiguration = {
			apiProvider: "zhanlu" as const,
			zhanluModelId: "deepseek-v3",
			zhanluAccessKey: "",
			zhanluSecretKey: "",
			zhanluToken: "",
			diffEnabled: true,
			fuzzyMatchThreshold: 1,
			rateLimitSeconds: 0,
		}

		// Simulate the logic that should happen in startNewTask
		const shouldCreateProfile = !!zhanluConfiguration.apiProvider
		const expectedProfileName = `eval-${zhanluConfiguration.apiProvider}-${Date.now()}`

		// Verify that the configuration has the correct provider
		expect(zhanluConfiguration.apiProvider).toBe("zhanlu")
		expect(zhanluConfiguration.zhanluModelId).toBe("deepseek-v3")
		expect(shouldCreateProfile).toBe(true)
		expect(expectedProfileName).toMatch(/^eval-zhanlu-\d+$/)
	})

	test("should not create profile when no apiProvider is specified", () => {
		// Mock configuration without apiProvider
		const configurationWithoutProvider: any = {
			diffEnabled: true,
			fuzzyMatchThreshold: 1,
			rateLimitSeconds: 0,
			customInstructions: "Test instructions",
		}

		// Simulate the logic that should happen in startNewTask
		const shouldCreateProfile = !!configurationWithoutProvider.apiProvider

		// Verify that no profile should be created
		expect(configurationWithoutProvider.apiProvider).toBeUndefined()
		expect(shouldCreateProfile).toBe(false)
	})

	test("should merge existing provider settings with evaluation configuration", () => {
		// Mock existing provider settings (e.g., from auto-login)
		const existingProviderSettings = {
			apiProvider: "zhanlu" as const,
			zhanluAccessKey: "",
			zhanluSecretKey: "",
			zhanluToken: "",
			diffEnabled: false,
			fuzzyMatchThreshold: 0.8,
		}

		// Mock evaluation configuration that wants to use OpenRouter
		const evaluationConfiguration = {
			apiProvider: "openrouter" as const,
			openRouterModelId: "gpt-4-turbo",
			openRouterApiKey: "sk-or-123456",
			diffEnabled: true,
			fuzzyMatchThreshold: 1,
		}

		// Simulate the merge that should happen in upsertProviderProfile
		const mergedProfile = {
			...existingProviderSettings,
			...evaluationConfiguration,
		}

		// Verify that the evaluation configuration overrides existing settings
		expect(mergedProfile.apiProvider).toBe("openrouter")
		expect(mergedProfile.openRouterModelId).toBe("gpt-4-turbo")
		expect(mergedProfile.openRouterApiKey).toBe("sk-or-123456")
		expect(mergedProfile.diffEnabled).toBe(true)
		expect(mergedProfile.fuzzyMatchThreshold).toBe(1)

		// Verify that existing Zhanlu settings are preserved (though not used)
		expect(mergedProfile.zhanluAccessKey).toBe("")
		expect(mergedProfile.zhanluSecretKey).toBe("")
		expect(mergedProfile.zhanluToken).toBe("")
	})
})
