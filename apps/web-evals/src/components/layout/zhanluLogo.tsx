"use client"

import { SVGProps, useEffect, useRef } from "react"
import { useRouter } from "next/navigation"
import { useHover } from "react-use"

type LogoProps = Omit<SVGProps<SVGSVGElement>, "xmlns" | "viewBox" | "onClick">

export const Logo = ({ width = 50, height = 32, fill = "#fff", className, ...props }: LogoProps) => {
	const router = useRouter()

	return (
		<svg
			width="207.116211"
			height="43.781982"
			className={className}
			viewBox="0 0 207.116 43.782"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			xmlnsXlink="http://www.w3.org/1999/xlink">
			<desc>Created with Pixso.</desc>
			<defs />
			<path
				id="路径 72"
				d="M11.62 12.88L2.97 15.57C2.97 15.57 0 16.25 0 19.29C0 22.33 0 41.9 0 41.9C0 41.9 0.7 44.63 4.29 43.39C7.89 42.15 25.19 35.81 25.63 35.61C26.07 35.42 26.43 35.23 26.45 35.22C27.05 33.33 25.92 32.12 23.56 31.43C19.78 30.33 14.74 28.34 14.74 28.34C14.74 28.34 11.62 26.96 11.62 24.9C11.62 22.84 11.62 12.88 11.62 12.88Z"
				fill="#9C6BFF"
				fillOpacity="1.000000"
				fillRule="evenodd"
			/>
			<path id="路径 72" d="" fill="#979797" fillOpacity="0" fillRule="evenodd" />
			<path
				id="路径 71"
				d="M11.62 2.46C11.62 0.88 12.22 -0.5 15.01 0.17C17.81 0.84 25.64 3.83 26.01 4.03C26.37 4.22 26.49 4.29 26.7 4.95C26.94 5.72 26.67 6.84 24.91 7.75C22.64 8.92 11.62 12.88 11.62 12.88C11.62 12.88 11.62 4.03 11.62 2.46Z"
				fill="#07D3DB"
				fillOpacity="1.000000"
				fillRule="evenodd"
			/>
			<path id="路径 71" d="" fill="#979797" fillOpacity="0" fillRule="evenodd" />
			<path
				id="路径 73"
				d="M11.62 12.88C11.62 12.88 11.62 23.4 11.62 24.94C11.62 26.48 11.57 26.97 14.49 28.29C17.41 29.61 24.74 31.92 24.74 31.92C24.74 31.92 27.15 32.98 26.45 35.22C27.77 34.54 27.99 34.22 27.99 31.59C27.99 28.96 27.99 7.4 27.99 6.44C27.99 5.47 28.02 4.75 26.15 4.1C26.91 4.96 26.94 6.32 25.59 7.33C24.23 8.35 11.62 12.88 11.62 12.88Z"
				fill="#422FE4"
				fillOpacity="1.000000"
				fillRule="evenodd"
			/>
			<path id="路径 73" d="" fill="#979797" fillOpacity="0" fillRule="evenodd" />
			<path
				id="路径"
				d="M176.99 6.5L170.76 6.5L160.28 36.46L166.61 36.46L176.99 6.5Z"
				fill="#10C1C8"
				fillOpacity="1.000000"
				fillRule="evenodd"
			/>
			<path id="路径" d="" fill="#979797" fillOpacity="0" fillRule="evenodd" />
			<path
				id="路径"
				d="M36.96 31.17L53.69 11.34L53.65 11.08L37.88 11.08L37.88 6.09L61.04 6.09L61.04 11.16L44.12 31.17L44.12 31.43L62.2 31.43L62.2 36.46L36.96 36.46L36.96 31.17Z"
				fill="#FFFFFF"
				fillOpacity="1.000000"
				fillRule="evenodd"
			/>
			<path id="路径" d="" fill="#FFFFFF" fillOpacity="0" fillRule="evenodd" />
			<path
				id="路径"
				d="M92.32 6.09L92.32 36.46L86.21 36.46L86.21 22.59L72.37 22.59L72.37 36.46L66.27 36.46L66.27 6.09L72.37 6.09L72.37 17.82L86.21 17.82L86.21 6.09L92.32 6.09Z"
				fill="#FFFFFF"
				fillOpacity="1.000000"
				fillRule="evenodd"
			/>
			<path id="路径" d="" fill="#FFFFFF" fillOpacity="0" fillRule="evenodd" />
			<mask id="mask_242_4691" fill="white">
				<path
					id="形状"
					d="M117.111 28.4138L103.673 28.4138L100.615 36.467L94.3594 36.467L106.407 6.09229L114.562 6.09229L126.517 36.467L120.077 36.467L117.111 28.4138ZM110.717 11.2131L115.304 23.5994L105.48 23.5994L110.161 11.2131L110.717 11.2131Z"
					clipRule="evenodd"
					fill=""
					fillOpacity="1.000000"
					fillRule="evenodd"
				/>
			</mask>
			<path
				id="形状"
				d="M117.111 28.4138L103.673 28.4138L100.615 36.467L94.3594 36.467L106.407 6.09229L114.562 6.09229L126.517 36.467L120.077 36.467L117.111 28.4138ZM110.717 11.2131L115.304 23.5994L105.48 23.5994L110.161 11.2131L110.717 11.2131Z"
				clipRule="evenodd"
				fill="#FFFFFF"
				fillOpacity="1.000000"
				fillRule="evenodd"
				mask="url(#mask_242_4691)"
			/>
			<path id="形状" d="" clipRule="evenodd" fill="#FFFFFF" fillOpacity="0.000000" fillRule="evenodd" />
			<path
				id="路径"
				d="M128.96 6.09L138.29 6.09L150 28.98L150.28 28.98L150.28 6.09L156.23 6.09L156.23 36.46L147.96 36.46L135.23 12.13L134.95 12.13L134.95 36.46L128.96 36.46L128.96 6.09Z"
				fill="#FFFFFF"
				fillOpacity="1.000000"
				fillRule="evenodd"
			/>
			<path id="路径" d="" fill="#FFFFFF" fillOpacity="0" fillRule="evenodd" />
			<path
				id="路径"
				d="M194.04 36.87C191.94 36.87 190.08 36.7 188.48 36.35C186.87 36.01 185.52 35.43 184.42 34.63C183.32 33.82 182.49 32.77 181.92 31.46C181.35 30.15 181.06 28.53 181.06 26.6L181.06 6.5L187.13 6.5L187.13 25.83C187.13 26.92 187.24 27.86 187.46 28.63C187.67 29.41 188.05 30.04 188.59 30.53C189.13 31.02 189.84 31.39 190.72 31.63C191.61 31.87 192.73 32 194.09 32C195.48 32 196.63 31.87 197.54 31.63C198.45 31.39 199.17 31 199.7 30.49C200.22 29.97 200.58 29.32 200.78 28.55C200.99 27.77 201.09 26.86 201.09 25.83L201.09 6.5L207.11 6.5L207.11 26.6C207.11 30.26 205.99 32.88 203.75 34.48C201.51 36.07 198.27 36.87 194.04 36.87Z"
				fill="#FFFFFF"
				fillOpacity="1.000000"
				fillRule="evenodd"
			/>
			<path id="路径" d="" fill="#FFFFFF" fillOpacity="0" fillRule="evenodd" />
		</svg>
	)
}

export const ZhanluLogo = (props: LogoProps) => {
	const ref = useRef<SVGSVGElement>(null)
	const logo = <Logo ref={ref} {...props} />
	const [hoverable, hovered] = useHover(logo)

	useEffect(() => {
		const element = ref.current
		const isHopping = element !== null && element.classList.contains("animate-hop")

		if (hovered && element && !isHopping) {
			element.classList.add("animate-hop")
		} else if (element && isHopping) {
			const onAnimationEnd = () => {
				element.classList.remove("animate-hop")
				element.removeEventListener("animationiteration", onAnimationEnd)
			}

			element.addEventListener("animationiteration", onAnimationEnd)
		}
	}, [hovered])

	return hoverable
}
