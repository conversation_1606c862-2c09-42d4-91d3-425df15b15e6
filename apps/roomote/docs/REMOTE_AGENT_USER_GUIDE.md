# 🚀 湛卢 Remote Agent 功能使用指南

## 📋 概述

Remote Agent（远程代理）是湛卢VS插件中的一个**实验性功能**，允许您在隔离的云端环境中执行AI驱动的开发任务。即使您关闭本地VS Code，Remote Agent也会在云端持续运行，完成代码分析、修复、测试和提交等任务。

### ✨ 核心特性

- **🌐 云端执行**：任务在隔离的Docker容器中运行，不影响本地环境
- **🤖 AI驱动**：使用您配置的AI模型自动分析和修复代码
- **🔗 多平台集成**：支持GitLab、JIRA工具平台集成
- **🔄 智能配置**：自动同步您的AI配置和执行模式

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   湛卢VS插件     │    │   Roomote API   │    │  Worker容器     │
│                 │    │                 │    │                 │
│ • 任务配置界面   │───▶│ • 任务队列管理   │───▶│ • VS Code实例   │
│ • 聊天界面      │    │ • 状态监控      │    │ • AI代理执行    │
│ • 历史记录      │    │ • 结果存储      │    │ • 代码修复      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🎯 使用步骤

### 步骤1：打开Remote Agent界面

1. 在VS Code中打开湛卢VS插件
2. 点击插件顶部菜单的"设置"按钮，找到"实验性"功能菜单
3. 点击"启用Remote Agent"，输入Remote Agent API 地址，这里填写研发测试环境"http://100.71.36.4:3001",点击验证连接，确保显示"已连接"，然后点击左上角"保存"按钮，在点击"完成"按钮
4. 在插件的顶部菜单会出现"云朵"的图标，点击"云朵"图标
5. 在打开的Remote Agent页面上，点击链接JIRA，输入用户名和弥密码，点击搜索并选择功能列表，选择指定要修复的JIRA
6. 点击链接到Gitlab,输入Gitlab的PAT Token，登录Gitlab，访问 "http://gitlab.cmss.com/-/profile/personal_access_tokens" 获取PAT Token
7. 完成项目加载后，选择代码库和分支，在自定义指令里面，输入想要额外补充命令和信息
8. 在自定义指令的对话框下面，选择在远程代码里面使用的模式和配置文件
9. 点击"启动远程代理"按钮，即完成任务提交，在新的页面上会出现实时更新任务进度，你可以关闭页面，任务完成后，会自动创建gitlab MR，完成MR评审后，可以合入到代码库

### 步骤2：配置GitLab连接

1. 点击 **"连接到GitLab"** 按钮
2. 输入您的GitLab服务器地址（如：`http://gitlab.cmss.com`）
3. 输入您的GitLab Personal Access Token
4. 点击 **"连接"** 验证配置
5. 选择要操作的仓库和分支

### 步骤3：配置AI设置

1. 在 **"AI配置"** 部分选择您要使用的AI提供商
2. 选择对应的模型（如：zhanluAI、GPT-4等）
3. 选择执行模式（如：code、architect等）

### 步骤4：编写任务指令

根据您的需求选择任务类型：

#### 选项A：纯自定义指令任务

- 在 **"任务描述"** 文本框中输入您的指令
- 例如：`"请为项目添加一个新的用户管理功能，包括用户注册、登录和权限管理"`

#### 选项B：JIRA工单任务（可选）

- 点击 **"连接到JIRA"** 配置JIRA连接
- 选择要处理的JIRA工单
- 可以添加额外的自定义指令来补充工单要求

### 步骤5：启动Remote Agent

1. 点击 **"启动远程代理"** 按钮
2. 系统将创建任务并显示任务ID
3. 自动跳转到聊天界面，您可以实时查看执行进度

## 💬 聊天界面使用

### 实时消息查看

Remote Agent聊天界面显示：

- **任务状态**：pending（等待中）、processing（执行中）、completed（已完成）、failed（失败）
- **AI对话**：与AI代理的实时对话内容
- **执行日志**：代码修改、命令执行等详细信息

### 界面功能

- **📱 消息列表**：显示所有对话消息，支持滚动查看
- **🔄 状态指示器**：实时显示任务执行状态
- **📊 任务信息**：显示任务ID、创建时间、配置信息等
- **⬅️ 返回按钮**：返回主配置界面

### 消息类型

- **🤖 AI回复**：AI代理的分析和建议
- **🔧 工具调用**：文件读取、代码修改等操作
- **💻 命令执行**：终端命令的执行结果
- **📝 代码生成**：AI生成的代码片段

## 📊 任务类型详解

### 1. roomote.custom（自定义指令任务）

**适用场景**：纯代码开发任务，不涉及JIRA工单

**特点**：

- 完全基于您提供的指令执行
- 支持任意GitLab仓库和分支
- 灵活的任务描述和AI配置

**示例指令**：

```
"请为React项目添加一个用户认证系统，包括：
1. 用户注册和登录页面
2. JWT token认证
3. 路由保护
4. 用户信息管理"
```

### 2. gitlab.jira.fix（JIRA工单修复）

**适用场景**：基于JIRA工单的代码修复任务

**特点**：

- 自动解析JIRA工单信息
- 结合工单描述和自定义指令
- 支持指定修复分支

**工作流程**：

1. 解析JIRA工单标题、描述、优先级等信息
2. 结合您的自定义指令
3. 在指定分支上执行修复
4. 创建Merge Request

## 🎉 总结

Remote Agent为湛卢VS插件提供了强大的云端执行能力，让您可以在隔离环境中安全地执行AI驱动的开发任务。通过合理配置和最佳实践，您可以充分利用这一实验性功能来提升开发效率。

记住，这是一个实验性功能，建议在非关键项目中先进行测试，熟悉其工作流程后再用于重要项目。

祝您使用愉快！🚀
