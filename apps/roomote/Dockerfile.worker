# docker compose build worker
# Note: Requires $GH_TOKEN to be set as build argument.

FROM roomote-base AS base

# Install additional worker-specific packages
RUN apt update && \
  apt install -y \
  xvfb \
  && rm -rf /var/lib/apt/lists/*

# Install VS Code
RUN wget -qO- https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor > packages.microsoft.gpg \
  && install -D -o root -g root -m 644 packages.microsoft.gpg /etc/apt/keyrings/packages.microsoft.gpg \
  && echo "deb [arch=amd64,arm64,armhf signed-by=/etc/apt/keyrings/packages.microsoft.gpg] https://packages.microsoft.com/repos/code stable main" | tee /etc/apt/sources.list.d/vscode.list > /dev/null \
  && rm -f packages.microsoft.gpg \
  && apt update && apt install -y code \
  cmake \
  golang-go \
  default-jre \
  python3 \
  python3-venv \
  python3-dev \
  python3-pip \
  tzdata \
  ntpdate \
  && rm -rf /var/lib/apt/lists/*

WORKDIR /roo

# Copy local compiled extension
COPY bin/*.vsix /tmp/

# Install VS Code extensions
ARG GOLANG_EXT_VERSION=0.46.1
ARG ESLINT_EXT_VERSION=3.0.10
ARG JAVA_EXT_VERSION=1.42.0
ARG PYTHON_EXT_VERSION=2025.6.1
ARG RUST_EXT_VERSION=0.3.2482

# Install extensions
RUN mkdir -p /roo/.vscode \
  && code --no-sandbox --user-data-dir /roo/.vscode --install-extension dbaeumer.vscode-eslint \
  && code --no-sandbox --user-data-dir /roo/.vscode --install-extension esbenp.prettier-vscode \
  && code --no-sandbox --user-data-dir /roo/.vscode --install-extension csstools.postcss \
  && code --no-sandbox --user-data-dir /roo/.vscode --install-extension golang.go@${GOLANG_EXT_VERSION} \
  && code --no-sandbox --user-data-dir /roo/.vscode --install-extension dbaeumer.vscode-eslint@${ESLINT_EXT_VERSION} \
  && code --no-sandbox --user-data-dir /roo/.vscode --install-extension redhat.java@${JAVA_EXT_VERSION} \
  && code --no-sandbox --user-data-dir /roo/.vscode --install-extension ms-python.python@${PYTHON_EXT_VERSION} \
  && code --no-sandbox --user-data-dir /roo/.vscode --install-extension rust-lang.rust-analyzer@${RUST_EXT_VERSION} \
  && code --no-sandbox --user-data-dir /roo/.vscode --install-extension /tmp/*.vsix \
  && rm /tmp/*.vsix

# Configure Git globally (requires $GH_TOKEN)
ARG GH_TOKEN
ENV GH_TOKEN=${GH_TOKEN}

# 接受GitLab PAT作为构建参数
ARG GITLAB_PAT
ENV GITLAB_PAT=${GITLAB_PAT}

# Setup Git credentials and configuration for dynamic repository access
RUN mkdir -p /roo/repos \
  && git config --global user.email "<EMAIL>" \
  && git config --global user.name "zhanlu" \
  && git config --global credential.helper store \
  && echo "https://oauth2:${GH_TOKEN}@github.com" > ~/.git-credentials

# 配置GitLab认证 (如果提供了GITLAB_PAT)
RUN if [ -n "${GITLAB_PAT}" ]; then \
      echo "http://oauth2:${GITLAB_PAT}@gitlab.cmss.com" >> ~/.git-credentials && \
      echo "https://oauth2:${GITLAB_PAT}@gitlab.cmss.com" >> ~/.git-credentials && \
      echo "http://oauth2:${GITLAB_PAT}@gitlab.example2000.com" >> ~/.git-credentials && \
      echo "https://oauth2:${GITLAB_PAT}@gitlab.example2000.com" >> ~/.git-credentials; \
    fi

# 预配置glab认证 (如果提供了GITLAB_PAT)
RUN if [ -n "${GITLAB_PAT}" ]; then \
      mkdir -p /root/.config/glab-cli && \
      echo "hosts:" > /root/.config/glab-cli/config.yml && \
      echo "  gitlab.cmss.com:" >> /root/.config/glab-cli/config.yml && \
      echo "    api_protocol: http" >> /root/.config/glab-cli/config.yml && \
      echo "    api_host: gitlab.cmss.com" >> /root/.config/glab-cli/config.yml && \
      echo "    token: ${GITLAB_PAT}" >> /root/.config/glab-cli/config.yml && \
      echo "    git_protocol: http" >> /root/.config/glab-cli/config.yml && \
      echo "    user: \"\"" >> /root/.config/glab-cli/config.yml && \
      echo "  gitlab.example2000.com:" >> /root/.config/glab-cli/config.yml && \
      echo "    api_protocol: http" >> /root/.config/glab-cli/config.yml && \
      echo "    api_host: gitlab.example2000.com" >> /root/.config/glab-cli/config.yml && \
      echo "    token: ${GITLAB_PAT}" >> /root/.config/glab-cli/config.yml && \
      echo "    git_protocol: http" >> /root/.config/glab-cli/config.yml && \
      echo "    user: \"\"" >> /root/.config/glab-cli/config.yml && \
      chmod 600 /root/.config/glab-cli/config.yml; \
    fi

# 复制GitLab认证配置脚本
COPY apps/roomote/scripts/configure-gitlab-auth.sh /usr/local/bin/configure-gitlab-auth.sh
RUN chmod +x /usr/local/bin/configure-gitlab-auth.sh

# Install dependencies
WORKDIR /roo
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY packages/config-eslint/package.json ./packages/config-eslint/
COPY packages/config-typescript/package.json ./packages/config-typescript/
COPY packages/types/package.json ./packages/types/
COPY packages/ipc/package.json ./packages/ipc/
COPY apps/roomote/package.json ./apps/roomote/

COPY scripts/bootstrap.mjs ./scripts/
RUN pnpm install

COPY apps/roomote ./apps/roomote/
COPY packages/config-eslint ./packages/config-eslint/
COPY packages/config-typescript ./packages/config-typescript/
COPY packages/types ./packages/types/
COPY packages/ipc ./packages/ipc/

WORKDIR /roo/apps/roomote
ENV NODE_ENV=production
CMD ["/bin/bash", "-c", "/usr/local/bin/configure-gitlab-auth.sh && pnpm worker"]
