services:
    base:
        build:
            context: ../../
            dockerfile: apps/roomote/Dockerfile.base
        image: roomote-base

    db:
        container_name: roomote-db
        image: postgres:17.5
        ports:
            - "5433:5432"
        volumes:
            - ./.docker/postgres:/var/lib/postgresql/data
        environment:
            - TZ=Asia/Shanghai
            - POSTGRES_USER=postgres
            - POSTGRES_PASSWORD=password
            - POSTGRES_DB=cloud_agents
        healthcheck:
            test: ["CMD-SHELL", "pg_isready -U postgres -d cloud_agents"]
            interval: 5s
            timeout: 5s
            retries: 5
            start_period: 30s
        # docker postgresql could not execute command ""/usr/lib/postgresql/17/bin/postgres" -V": Cannot allocate memory initdb: error: program "postgres" is needed by initdb but was not found in the same directory as "/usr/lib/postgresql/17/bin/initdb" could not execute command ""/usr/lib/postgresql/17/bin/postgres" -V": Cannot allocate memory
        privileged: true # 添加此行使容器以特权模式运行, 解决低版本Docker和docker-compose导致的启动失败的问题 https://github.com/timescale/timescaledb-docker-ha/issues/260

    redis:
        container_name: roomote-redis
        image: redis:7-alpine
        ports:
            - "6380:6379"
        volumes:
            - ./.docker/redis:/data
        environment:
            - TZ=Asia/Shanghai
        command: redis-server --appendonly yes

    dashboard:
        container_name: roomote-dashboard
        build:
            context: ../../
            dockerfile: apps/roomote/Dockerfile.dashboard
        image: roomote-dashboard
        ports:
            - "3002:3002"
        environment:
            - TZ=Asia/Shanghai
            - REDIS_URL=redis://redis:6379
            - NODE_ENV=production
        depends_on:
            redis:
                condition: service_started

    api:
        container_name: roomote-api
        build:
            context: ../../
            dockerfile: apps/roomote/Dockerfile.api
        image: roomote-api
        ports:
            - "3001:3001"
        environment:
            - TZ=Asia/Shanghai
            - DATABASE_URL=**************************************/cloud_agents
            - REDIS_URL=redis://redis:6379
            - NODE_ENV=production
        volumes:
            - /run/user/1000/docker.sock:/var/run/docker.sock
            # 添加消息共享目录映射，使API能访问消息文件
            - /tmp/roomote/messages:/roo/shared/messages
        depends_on:
            db:
                condition: service_healthy
            redis:
                condition: service_started

    controller:
        container_name: roomote-controller
        build:
            context: ../../
            dockerfile: apps/roomote/Dockerfile.controller
            args:
                - GH_TOKEN=${GH_TOKEN}
        image: roomote-controller
        env_file:
            - .env
        environment:
            - TZ=Asia/Shanghai
            - HOST_EXECUTION_METHOD=docker
            - DATABASE_URL=**************************************/cloud_agents
            - REDIS_URL=redis://redis:6379
            - NODE_ENV=production
            # Docker hosts映射控制
            - ENABLE_HOST_MAPPING=${ENABLE_HOST_MAPPING:-false}
            - DOCKER_HOST_MAPPINGS=${DOCKER_HOST_MAPPINGS:-}
            # 依赖安装控制环境变量
            - SKIP_DEPENDENCY_INSTALL=${SKIP_DEPENDENCY_INSTALL:-false}
        volumes:
            - /run/user/1000/docker.sock:/var/run/docker.sock
            - /tmp/roomote:/var/log/roomote
        depends_on:
            db:
                condition: service_healthy
            redis:
                condition: service_started
        restart: unless-stopped

    worker:
        build:
            context: ../../
            dockerfile: apps/roomote/Dockerfile.worker
            args:
                - GH_TOKEN=${GH_TOKEN}
                - GITLAB_PAT=${GITLAB_PAT}
        image: roomote-worker
        env_file:
            - .env
        environment:
            - TZ=Asia/Shanghai
            - HOST_EXECUTION_METHOD=docker
            - DATABASE_URL=**************************************/cloud_agents
            - REDIS_URL=redis://redis:6379
            - NODE_ENV=production
            # GitLab 配置
            - GITLAB_URL=http://gitlab.cmss.com
            # 依赖安装控制环境变量
            - SKIP_DEPENDENCY_INSTALL=${SKIP_DEPENDENCY_INSTALL:-false}
        volumes:
            - /run/user/1000/docker.sock:/var/run/docker.sock
            - /tmp/roomote:/var/log/roomote
            # 新增：映射VS Code globalStorage目录
            - /tmp/roomote/vscode-storage:/roo/.vscode/User/globalStorage
            # 新增：映射消息共享目录
            - /tmp/roomote/messages:/roo/shared/messages
        stdin_open: true
        tty: true
        depends_on:
            db:
                condition: service_healthy
            redis:
                condition: service_started

networks:
    default:
        name: roomote_default
        driver: bridge
